/**
 * <PERSON><PERSON><PERSON> Sistemi - JavaScript Axtarış Funksiyaları
 * 
 * Bu fayl real-time axtarış və təkliflər üçün JavaScript kodlarını ehtiva edir
 */

class DrugSearchSuggestions {
    constructor() {
        this.searchInput = null;
        this.suggestionsContainer = null;
        this.currentQuery = '';
        this.searchTimeout = null;
        this.selectedIndex = -1;
        this.suggestions = [];
        this.isLoading = false;
        
        this.init();
    }
    
    init() {
        // DOM elementlərini tap
        this.searchInput = document.querySelector('.search-input');
        
        if (!this.searchInput) {
            console.warn('Search input not found');
            return;
        }
        
        // Suggestions container yarat
        this.createSuggestionsContainer();
        
        // Event listener-ləri əlavə et
        this.attachEventListeners();
    }
    
    createSuggestionsContainer() {
        // Mövcud container-i tap və ya yarat
        this.suggestionsContainer = document.querySelector('.search-suggestions');
        
        if (!this.suggestionsContainer) {
            this.suggestionsContainer = document.createElement('div');
            this.suggestionsContainer.className = 'search-suggestions';
            
            // Search wrapper-ə əlavə et
            const searchWrapper = this.searchInput.closest('.search-wrapper');
            if (searchWrapper) {
                searchWrapper.appendChild(this.suggestionsContainer);
            }
        }
    }
    
    attachEventListeners() {
        // Input events
        this.searchInput.addEventListener('input', (e) => {
            this.handleInput(e.target.value);
        });
        
        this.searchInput.addEventListener('keydown', (e) => {
            this.handleKeydown(e);
        });
        
        this.searchInput.addEventListener('focus', () => {
            if (this.currentQuery.length >= 2) {
                this.showSuggestions();
            }
        });
        
        this.searchInput.addEventListener('blur', () => {
            // Kiçik gecikməylə gizlət ki, click event işləsin
            setTimeout(() => {
                this.hideSuggestions();
            }, 150);
        });
        
        // Document click
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.search-wrapper')) {
                this.hideSuggestions();
            }
        });
    }
    
    handleInput(value) {
        const query = value.trim();
        this.currentQuery = query;
        this.selectedIndex = -1;
        
        // Timeout-u təmizlə
        if (this.searchTimeout) {
            clearTimeout(this.searchTimeout);
        }
        
        if (query.length < 2) {
            this.hideSuggestions();
            return;
        }
        
        // Debounce ilə axtarış et
        this.searchTimeout = setTimeout(() => {
            this.searchSuggestions(query);
        }, 200);
    }
    
    handleKeydown(e) {
        if (!this.suggestionsContainer.classList.contains('show')) {
            return;
        }
        
        switch (e.key) {
            case 'ArrowDown':
                e.preventDefault();
                this.navigateSuggestions(1);
                break;
                
            case 'ArrowUp':
                e.preventDefault();
                this.navigateSuggestions(-1);
                break;
                
            case 'Enter':
                e.preventDefault();
                if (this.selectedIndex >= 0 && this.suggestions[this.selectedIndex]) {
                    this.selectSuggestion(this.suggestions[this.selectedIndex]);
                }
                break;
                
            case 'Escape':
                this.hideSuggestions();
                break;
        }
    }
    
    navigateSuggestions(direction) {
        const maxIndex = this.suggestions.length - 1;
        
        if (direction > 0) {
            this.selectedIndex = this.selectedIndex < maxIndex ? this.selectedIndex + 1 : 0;
        } else {
            this.selectedIndex = this.selectedIndex > 0 ? this.selectedIndex - 1 : maxIndex;
        }
        
        this.updateSuggestionHighlight();
    }
    
    updateSuggestionHighlight() {
        const items = this.suggestionsContainer.querySelectorAll('.suggestion-item');
        
        items.forEach((item, index) => {
            if (index === this.selectedIndex) {
                item.classList.add('highlighted');
            } else {
                item.classList.remove('highlighted');
            }
        });
    }
    
    async searchSuggestions(query) {
        if (this.isLoading) {
            return;
        }
        
        this.isLoading = true;
        this.showLoadingState();
        
        try {
            const response = await fetch('includes/search_api.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `query=${encodeURIComponent(query)}&action=suggestions`
            });
            
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            
            const data = await response.json();
            
            if (data.success) {
                this.suggestions = data.suggestions || [];
                this.displaySuggestions(this.suggestions);
            } else {
                this.showNoSuggestions();
            }
            
        } catch (error) {
            console.error('Search suggestions error:', error);
            this.showNoSuggestions();
        } finally {
            this.isLoading = false;
        }
    }
    
    showLoadingState() {
        this.suggestionsContainer.innerHTML = `
            <div class="loading-suggestions">
                🔍 Axtarılır...
            </div>
        `;
        this.showSuggestions();
    }
    
    displaySuggestions(suggestions) {
        if (!suggestions || suggestions.length === 0) {
            this.showNoSuggestions();
            return;
        }
        
        const html = suggestions.map((suggestion, index) => {
            const highlightedText = this.highlightQuery(suggestion.text, this.currentQuery);
            const countText = suggestion.count > 1 ? `(${suggestion.count})` : '';
            
            return `
                <div class="suggestion-item" data-index="${index}" data-text="${this.escapeHtml(suggestion.text)}">
                    <span class="suggestion-icon">🔍</span>
                    <span class="suggestion-text">${highlightedText}</span>
                    <span class="suggestion-count">${countText}</span>
                </div>
            `;
        }).join('');
        
        this.suggestionsContainer.innerHTML = html;
        
        // Click event-lərini əlavə et
        this.attachSuggestionClickEvents();
        
        this.showSuggestions();
    }
    
    showNoSuggestions() {
        this.suggestionsContainer.innerHTML = `
            <div class="no-suggestions">
                Heç bir təklif tapılmadı
            </div>
        `;
        this.showSuggestions();
    }
    
    attachSuggestionClickEvents() {
        const items = this.suggestionsContainer.querySelectorAll('.suggestion-item');
        
        items.forEach((item, index) => {
            item.addEventListener('click', () => {
                const suggestion = this.suggestions[index];
                if (suggestion) {
                    this.selectSuggestion(suggestion);
                }
            });
        });
    }
    
    selectSuggestion(suggestion) {
        this.searchInput.value = suggestion.text;
        this.hideSuggestions();
        
        // Form-u submit et
        const form = this.searchInput.closest('form');
        if (form) {
            form.submit();
        }
    }
    
    highlightQuery(text, query) {
        if (!query) return this.escapeHtml(text);
        
        const escapedText = this.escapeHtml(text);
        const escapedQuery = this.escapeHtml(query);
        
        // Case-insensitive highlight
        const regex = new RegExp(`(${escapedQuery})`, 'gi');
        return escapedText.replace(regex, '<span class="highlight">$1</span>');
    }
    
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
    
    showSuggestions() {
        this.suggestionsContainer.classList.add('show');
    }
    
    hideSuggestions() {
        this.suggestionsContainer.classList.remove('show');
        this.selectedIndex = -1;
    }
}

// DOM yüklənəndə başlat
document.addEventListener('DOMContentLoaded', () => {
    new DrugSearchSuggestions();
});
