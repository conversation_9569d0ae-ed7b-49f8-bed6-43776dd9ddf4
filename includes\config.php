<?php
/**
 * <PERSON><PERSON><PERSON>ş Sistemi - Konfiqurasiya
 * 
 * Bu fayl sistemin əsas konfiqurasiya parametrlərini ehtiva edir
 */

// Sistem konfiqurasiyası
define('SYSTEM_NAME', '<PERSON>ərman Axtarış Sistemi');
define('SYSTEM_VERSION', '2.0.0');
define('SYSTEM_DESCRIPTION', 'Təsir<PERSON>i maddə əsasında dərmanları tapın');

// Fayl yolları
define('DATA_FILE', __DIR__ . '/../data.json');
define('HISTORY_FILE', __DIR__ . '/../global_search_history.json');

// Axtarış konfiqurasiyası
define('MAX_SEARCH_RESULTS', 50);
define('MIN_SEARCH_LENGTH', 2);
define('MAX_SEARCH_LENGTH', 50);
define('MAX_HISTORY_ITEMS', 10);

// Performance konfiqurasiyası
define('ENABLE_SEARCH_CACHE', false);
define('CACHE_DURATION', 300); // 5 dəqiqə

// Təhlükəsizlik konfiqurasiyası
define('ENABLE_RATE_LIMITING', false);
define('MAX_REQUESTS_PER_MINUTE', 60);

// Debug konfiqurasiyası
define('DEBUG_MODE', false);
define('LOG_SEARCHES', true);
define('LOG_ERRORS', true);

// UI konfiqurasiyası
define('RESULTS_PER_PAGE', 50);
define('SHOW_SEARCH_STATISTICS', false);
define('ENABLE_SEARCH_HIGHLIGHTING', true);

// Mətn konfiqurasiyası
define('DEFAULT_LANGUAGE', 'az');
define('ENABLE_NORMALIZATION', true);

/**
 * Sistem konfiqurasiyasını qaytarır
 * 
 * @return array Konfiqurasiya massivi
 */
function getSystemConfig() {
    return [
        'system' => [
            'name' => SYSTEM_NAME,
            'version' => SYSTEM_VERSION,
            'description' => SYSTEM_DESCRIPTION
        ],
        'files' => [
            'data' => DATA_FILE,
            'history' => HISTORY_FILE
        ],
        'search' => [
            'max_results' => MAX_SEARCH_RESULTS,
            'min_length' => MIN_SEARCH_LENGTH,
            'max_length' => MAX_SEARCH_LENGTH,
            'enable_cache' => ENABLE_SEARCH_CACHE,
            'cache_duration' => CACHE_DURATION
        ],
        'history' => [
            'max_items' => MAX_HISTORY_ITEMS
        ],
        'security' => [
            'rate_limiting' => ENABLE_RATE_LIMITING,
            'max_requests' => MAX_REQUESTS_PER_MINUTE
        ],
        'ui' => [
            'results_per_page' => RESULTS_PER_PAGE,
            'show_statistics' => SHOW_SEARCH_STATISTICS,
            'enable_highlighting' => ENABLE_SEARCH_HIGHLIGHTING
        ],
        'debug' => [
            'mode' => DEBUG_MODE,
            'log_searches' => LOG_SEARCHES,
            'log_errors' => LOG_ERRORS
        ]
    ];
}

/**
 * Debug rejimini yoxlayır
 * 
 * @return bool Debug rejimi aktivdirsə true
 */
function isDebugMode() {
    return DEBUG_MODE;
}

/**
 * Xəta loqu yazır (əgər aktiv edilibsə)
 * 
 * @param string $message Xəta mesajı
 * @param string $level Xəta səviyyəsi (error, warning, info)
 */
function logMessage($message, $level = 'info') {
    if (!LOG_ERRORS && $level === 'error') {
        return;
    }
    
    if (!LOG_SEARCHES && $level === 'search') {
        return;
    }
    
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[{$timestamp}] [{$level}] {$message}" . PHP_EOL;
    
    $logFile = __DIR__ . '/../logs/system.log';
    
    // Log qovluğunu yarat
    $logDir = dirname($logFile);
    if (!is_dir($logDir)) {
        mkdir($logDir, 0755, true);
    }
    
    file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);
}

/**
 * Sistem statusunu yoxlayır
 * 
 * @return array Status məlumatları
 */
function getSystemStatus() {
    $status = [
        'system' => 'ok',
        'data_file' => file_exists(DATA_FILE) ? 'ok' : 'missing',
        'history_file' => file_exists(HISTORY_FILE) ? 'ok' : 'missing',
        'writable' => is_writable(dirname(HISTORY_FILE)) ? 'ok' : 'readonly',
        'php_version' => PHP_VERSION,
        'memory_usage' => memory_get_usage(true),
        'timestamp' => time()
    ];
    
    // Ümumi status
    $hasErrors = in_array('missing', $status) || in_array('readonly', $status);
    $status['overall'] = $hasErrors ? 'error' : 'ok';
    
    return $status;
}
