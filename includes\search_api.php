<?php
/**
 * <PERSON><PERSON><PERSON> Axtarış Sistemi - AJAX API
 * 
 * Bu fayl JavaScript-dən gələn axtarış sorğularını işləyir
 */

// Headers
header('Content-Type: application/json; charset=utf-8');
header('Cache-Control: no-cache, must-revalidate');

// CORS (əgər lazımdırsa)
if (isset($_SERVER['HTTP_ORIGIN'])) {
    header("Access-Control-Allow-Origin: {$_SERVER['HTTP_ORIGIN']}");
    header('Access-Control-Allow-Credentials: true');
    header('Access-Control-Max-Age: 86400');
}

if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    if (isset($_SERVER['HTTP_ACCESS_CONTROL_REQUEST_METHOD'])) {
        header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
    }
    if (isset($_SERVER['HTTP_ACCESS_CONTROL_REQUEST_HEADERS'])) {
        header("Access-Control-Allow-Headers: {$_SERVER['HTTP_ACCESS_CONTROL_REQUEST_HEADERS']}");
    }
    exit(0);
}

// Lazımi faylları daxil et
require_once __DIR__ . '/config.php';
require_once __DIR__ . '/functions.php';
require_once __DIR__ . '/search.php';

/**
 * JSON cavab göndər
 */
function sendJsonResponse($data, $httpCode = 200) {
    http_response_code($httpCode);
    echo json_encode($data, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    exit;
}

/**
 * Xəta cavabı göndər
 */
function sendErrorResponse($message, $httpCode = 400) {
    sendJsonResponse([
        'success' => false,
        'error' => $message,
        'timestamp' => time()
    ], $httpCode);
}

// Yalnız POST sorğularını qəbul et
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    sendErrorResponse('Yalnız POST sorğuları dəstəklənir', 405);
}

// Parametrləri al
$action = $_POST['action'] ?? '';
$query = $_POST['query'] ?? '';

// Action yoxla
if (empty($action)) {
    sendErrorResponse('Action parametri tələb olunur');
}

// Query yoxla və təmizlə
if (empty($query)) {
    sendErrorResponse('Query parametri tələb olunur');
}

$query = sanitizeInput($query);
$validation = validateSearchQuery($query);

if (!$validation['valid']) {
    sendErrorResponse($validation['message']);
}

$query = $validation['query'];

try {
    switch ($action) {
        case 'suggestions':
            handleSuggestionsRequest($query);
            break;
            
        case 'search':
            handleSearchRequest($query);
            break;
            
        default:
            sendErrorResponse('Naməlum action: ' . $action);
    }
} catch (Exception $e) {
    logMessage("API xətası: " . $e->getMessage(), 'error');
    sendErrorResponse('Daxili server xətası');
}

/**
 * Təkliflər sorğusunu işlə
 */
function handleSuggestionsRequest($query) {
    // Məlumatları yüklə
    $drugsData = loadDrugsData();
    
    if (empty($drugsData)) {
        sendErrorResponse('Məlumat bazası əlçatan deyil');
    }
    
    // Təkliflər yarat
    $suggestions = generateSuggestions($query, $drugsData);
    
    sendJsonResponse([
        'success' => true,
        'query' => $query,
        'suggestions' => $suggestions,
        'count' => count($suggestions),
        'timestamp' => time()
    ]);
}

/**
 * Axtarış sorğusunu işlə
 */
function handleSearchRequest($query) {
    // Məlumatları yüklə
    $drugsData = loadDrugsData();
    
    if (empty($drugsData)) {
        sendErrorResponse('Məlumat bazası əlçatan deyil');
    }
    
    // Axtarış et
    $searchResult = searchDrugs($query, $drugsData, MAX_SEARCH_RESULTS);
    
    if (!$searchResult['success']) {
        sendErrorResponse($searchResult['message']);
    }
    
    sendJsonResponse([
        'success' => true,
        'query' => $query,
        'results' => $searchResult['results'],
        'total' => $searchResult['total'],
        'statistics' => getSearchStatistics($searchResult['results']),
        'timestamp' => time()
    ]);
}

/**
 * Axtarış təklifləri yarat
 */
function generateSuggestions($query, $drugsData) {
    $normalizedQuery = normalize($query);
    $suggestions = [];
    $seenTerms = [];
    
    // Maksimum təklif sayı
    $maxSuggestions = 8;
    
    foreach ($drugsData as $drug) {
        if (count($suggestions) >= $maxSuggestions) {
            break;
        }
        
        $tesiredici = $drug['tesiredici'] ?? '';
        $terkib = $drug['terkib'] ?? '';
        
        if (empty($tesiredici) || $tesiredici === '1') {
            continue;
        }
        
        $normalizedTesiredici = normalize($tesiredici);
        $normalizedTerkib = normalize($terkib);
        
        // Təsiredici maddədə axtarış
        if (strpos($normalizedTesiredici, $normalizedQuery) !== false) {
            $key = $normalizedTesiredici;
            
            if (!isset($seenTerms[$key])) {
                $seenTerms[$key] = [
                    'text' => $tesiredici,
                    'count' => 0,
                    'score' => calculateSuggestionScore($normalizedQuery, $normalizedTesiredici)
                ];
            }
            
            $seenTerms[$key]['count']++;
        }
        
        // Tərkibdə axtarış (əgər təsiredici tapılmadısa)
        if (!isset($seenTerms[$normalizedTesiredici]) && 
            strpos($normalizedTerkib, $normalizedQuery) !== false) {
            
            // Tərkibdən əsas sözləri çıxar
            $words = extractMainWords($terkib, $query);
            
            foreach ($words as $word) {
                $normalizedWord = normalize($word);
                
                if (!isset($seenTerms[$normalizedWord]) && 
                    strpos($normalizedWord, $normalizedQuery) !== false) {
                    
                    $seenTerms[$normalizedWord] = [
                        'text' => $word,
                        'count' => 1,
                        'score' => calculateSuggestionScore($normalizedQuery, $normalizedWord) - 100
                    ];
                }
            }
        }
    }
    
    // Score-a görə sırala
    uasort($seenTerms, function($a, $b) {
        if ($a['score'] === $b['score']) {
            return $b['count'] - $a['count'];
        }
        return $b['score'] - $a['score'];
    });
    
    // Nəticəni format et
    foreach ($seenTerms as $term) {
        $suggestions[] = [
            'text' => $term['text'],
            'count' => $term['count'],
            'score' => $term['score']
        ];
        
        if (count($suggestions) >= $maxSuggestions) {
            break;
        }
    }
    
    return $suggestions;
}

/**
 * Təklif score-u hesabla
 */
function calculateSuggestionScore($query, $text) {
    $score = 0;
    
    // Exact match
    if ($text === $query) {
        $score = 1000;
    }
    // Starts with
    elseif (strpos($text, $query) === 0) {
        $score = 800;
    }
    // Contains
    elseif (strpos($text, $query) !== false) {
        $score = 600;
        
        // Sözün əvvəlində olması daha yüksək score
        if (preg_match('/\b' . preg_quote($query, '/') . '/i', $text)) {
            $score += 100;
        }
    }
    
    // Uzunluq bonusu (qısa sözlər daha yaxşı)
    $lengthBonus = max(0, 50 - strlen($text));
    $score += $lengthBonus;
    
    return $score;
}

/**
 * Tərkibdən əsas sözləri çıxar
 */
function extractMainWords($text, $query) {
    // Ümumi sözləri çıxar
    $commonWords = ['tab', 'caps', 'susp', 'sol', 'inj', 'mg', 'ml', 'g', 'mcg'];
    
    // Sözlərə böl
    $words = preg_split('/[\s\-\+\(\)\[\]\/\\\\]+/', $text);
    $mainWords = [];
    
    foreach ($words as $word) {
        $word = trim($word, '.,!?;:');
        
        if (strlen($word) >= 3 && 
            !in_array(strtolower($word), $commonWords) &&
            !is_numeric($word)) {
            
            $mainWords[] = $word;
        }
    }
    
    return array_unique($mainWords);
}
?>
