<?php
/**
 * <PERSON><PERSON><PERSON>ş Sistemi - Status Səhifəsi
 * 
 * Bu fayl sistemin statusunu və statistikalarını göstərir
 */

// Təhlükəsizlik: yalnız localhost-dan giriş
if (!in_array($_SERVER['REMOTE_ADDR'], ['127.0.0.1', '::1'])) {
    http_response_code(403);
    die('Access denied');
}

require_once __DIR__ . '/../includes/config.php';
require_once __DIR__ . '/../includes/functions.php';
require_once __DIR__ . '/../includes/history.php';

// Sistem statusunu al
$systemStatus = getSystemStatus();
$historyStats = getHistoryStatistics();
$config = getSystemConfig();

// Məlumat faylı statistikaları
$dataStats = [];
if (file_exists(DATA_FILE)) {
    $dataStats = [
        'size' => filesize(DATA_FILE),
        'modified' => filemtime(DATA_FILE),
        'readable' => is_readable(DATA_FILE)
    ];
    
    // Məlumat sayını hesabla
    $drugsData = loadDrugsData();
    $dataStats['total_drugs'] = count($drugsData);
    $dataStats['valid_drugs'] = 0;
    
    foreach ($drugsData as $drug) {
        if (isset($drug['tesiredici']) && !empty($drug['tesiredici']) && $drug['tesiredici'] !== '1') {
            $dataStats['valid_drugs']++;
        }
    }
}

?>
<!DOCTYPE html>
<html lang="az">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sistem Statusu - <?= SYSTEM_NAME ?></title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; }
        .card { background: white; padding: 20px; margin: 20px 0; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .status-ok { color: #28a745; }
        .status-error { color: #dc3545; }
        .status-warning { color: #ffc107; }
        table { width: 100%; border-collapse: collapse; }
        th, td { padding: 8px 12px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background: #f8f9fa; }
        .metric { display: inline-block; margin: 10px 20px 10px 0; }
        .metric-value { font-size: 24px; font-weight: bold; color: #007bff; }
        .metric-label { font-size: 12px; color: #666; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 4px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Sistem Statusu</h1>
        
        <!-- Ümumi Status -->
        <div class="card">
            <h2>Ümumi Status</h2>
            <p>Status: <span class="status-<?= $systemStatus['overall'] ?>"><?= ucfirst($systemStatus['overall']) ?></span></p>
            <p>Sistem: <?= SYSTEM_NAME ?> v<?= SYSTEM_VERSION ?></p>
            <p>PHP Versiyası: <?= $systemStatus['php_version'] ?></p>
            <p>Yaddaş İstifadəsi: <?= round($systemStatus['memory_usage'] / 1024 / 1024, 2) ?> MB</p>
            <p>Son Yoxlama: <?= date('Y-m-d H:i:s', $systemStatus['timestamp']) ?></p>
        </div>

        <!-- Fayllar -->
        <div class="card">
            <h2>📁 Fayllar</h2>
            <table>
                <tr>
                    <th>Fayl</th>
                    <th>Status</th>
                    <th>Ölçü</th>
                    <th>Son Dəyişiklik</th>
                </tr>
                <tr>
                    <td>Məlumat Faylı</td>
                    <td><span class="status-<?= $systemStatus['data_file'] ?>"><?= ucfirst($systemStatus['data_file']) ?></span></td>
                    <td><?= isset($dataStats['size']) ? round($dataStats['size'] / 1024, 2) . ' KB' : 'N/A' ?></td>
                    <td><?= isset($dataStats['modified']) ? date('Y-m-d H:i:s', $dataStats['modified']) : 'N/A' ?></td>
                </tr>
                <tr>
                    <td>Tarixçə Faylı</td>
                    <td><span class="status-<?= $systemStatus['history_file'] ?>"><?= ucfirst($systemStatus['history_file']) ?></span></td>
                    <td><?= file_exists(HISTORY_FILE) ? round(filesize(HISTORY_FILE) / 1024, 2) . ' KB' : 'N/A' ?></td>
                    <td><?= file_exists(HISTORY_FILE) ? date('Y-m-d H:i:s', filemtime(HISTORY_FILE)) : 'N/A' ?></td>
                </tr>
            </table>
        </div>

        <!-- Məlumat Statistikaları -->
        <?php if (!empty($dataStats)): ?>
        <div class="card">
            <h2>📊 Məlumat Statistikaları</h2>
            <div class="metric">
                <div class="metric-value"><?= number_format($dataStats['total_drugs']) ?></div>
                <div class="metric-label">Ümumi Dərman</div>
            </div>
            <div class="metric">
                <div class="metric-value"><?= number_format($dataStats['valid_drugs']) ?></div>
                <div class="metric-label">Keçərli Dərman</div>
            </div>
            <div class="metric">
                <div class="metric-value"><?= $dataStats['total_drugs'] > 0 ? round(($dataStats['valid_drugs'] / $dataStats['total_drugs']) * 100, 1) : 0 ?>%</div>
                <div class="metric-label">Keçərlilik Nisbəti</div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Axtarış Tarixçəsi -->
        <div class="card">
            <h2>🔍 Axtarış Tarixçəsi</h2>
            <div class="metric">
                <div class="metric-value"><?= number_format($historyStats['total_searches']) ?></div>
                <div class="metric-label">Ümumi Axtarış</div>
            </div>
            <div class="metric">
                <div class="metric-value"><?= number_format($historyStats['unique_terms']) ?></div>
                <div class="metric-label">Unikal Termin</div>
            </div>
            <?php if ($historyStats['most_searched']): ?>
            <div class="metric">
                <div class="metric-value"><?= htmlspecialchars($historyStats['most_searched']['term']) ?></div>
                <div class="metric-label">Ən Çox Axtarılan (<?= $historyStats['most_searched']['count'] ?> dəfə)</div>
            </div>
            <?php endif; ?>
            
            <?php if (!empty($historyStats['recent_activity'])): ?>
            <h3>Son Axtarışlar</h3>
            <ul>
                <?php foreach ($historyStats['recent_activity'] as $term): ?>
                    <li><?= htmlspecialchars($term) ?></li>
                <?php endforeach; ?>
            </ul>
            <?php endif; ?>
        </div>

        <!-- Konfiqurasiya -->
        <div class="card">
            <h2>⚙️ Konfiqurasiya</h2>
            <pre><?= htmlspecialchars(json_encode($config, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)) ?></pre>
        </div>

        <!-- Əməliyyatlar -->
        <div class="card">
            <h2>🔧 Əməliyyatlar</h2>
            <p><a href="?action=clear_history" onclick="return confirm('Tarixçəni təmizləmək istədiyinizə əminsiniz?')">🗑️ Tarixçəni Təmizlə</a></p>
            <p><a href="../">🏠 Ana Səhifəyə Qayıt</a></p>
        </div>
    </div>

    <?php
    // Əməliyyatları işlə
    if (isset($_GET['action'])) {
        switch ($_GET['action']) {
            case 'clear_history':
                if (clearGlobalHistory()) {
                    echo '<script>alert("Tarixçə təmizləndi!"); window.location.href = "status.php";</script>';
                } else {
                    echo '<script>alert("Tarixçə təmizlənərkən xəta baş verdi!");</script>';
                }
                break;
        }
    }
    ?>
</body>
</html>
