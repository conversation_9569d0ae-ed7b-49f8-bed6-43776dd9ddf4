<?php
/**
 * <PERSON><PERSON><PERSON> Axtarış Sistemi - Tarixçə İdarəetməsi
 * 
 * Bu fayl axtarış tarixçəsi ilə bağlı funksiyaları ehtiva edir:
 * - Global tarixçə oxuma və yazma
 * - Tarixçə validasiyası
 * - Tarixçə təmizləmə
 */

require_once __DIR__ . '/functions.php';

/**
 * Global axtarış tarixçəsini oxuyur
 * 
 * @param int $limit Maksimum tarixçə sayı (default: 10)
 * @return array Tarixçə massivi
 */
function getGlobalHistory($limit = 10) {
    $historyFile = __DIR__ . '/../global_search_history.json';
    
    if (!file_exists($historyFile)) {
        // Fayl yoxdursa boş fayl yarat
        createEmptyHistoryFile($historyFile);
        return [];
    }
    
    $content = file_get_contents($historyFile);
    if ($content === false) {
        error_log("Tarixçə faylı oxuna bilmədi: " . $historyFile);
        return [];
    }
    
    $data = json_decode($content, true);
    if (json_last_error() !== JSON_ERROR_NONE) {
        error_log("Tarixçə JSON parse xətası: " . json_last_error_msg());
        return [];
    }
    
    if (!is_array($data)) {
        return [];
    }
    
    // Limit tətbiq et
    return array_slice($data, 0, $limit);
}

/**
 * Global tarixçəyə yeni axtarış əlavə edir
 * 
 * @param string $searchTerm Axtarış termini
 * @param int $maxItems Maksimum tarixçə sayı (default: 10)
 * @return bool Uğurlu əlavə edilib-edilmədiyi
 */
function addToGlobalHistory($searchTerm, $maxItems = 10) {
    $historyFile = __DIR__ . '/../global_search_history.json';
    
    // Giriş məlumatını validasiya et
    $validation = validateSearchQuery($searchTerm);
    if (!$validation['valid']) {
        return false;
    }
    
    $searchTerm = $validation['query'];
    
    // Mövcud tarixçəni oxu
    $history = getGlobalHistory($maxItems * 2); // Daha çox oxu ki, təmizləmə zamanı kifayət etsin
    
    $normalizedSearch = normalize($searchTerm);
    
    // Mövcud olanı sil (case-insensitive)
    $history = array_filter($history, function($item) use ($normalizedSearch) {
        return normalize($item) !== $normalizedSearch;
    });
    
    // Başa əlavə et
    array_unshift($history, $searchTerm);
    
    // Maksimum sayda saxla
    if (count($history) > $maxItems) {
        $history = array_slice($history, 0, $maxItems);
    }
    
    // Faylı yenilə
    return saveHistoryToFile($historyFile, $history);
}

/**
 * Tarixçəni faylda saxlayır
 * 
 * @param string $filePath Fayl yolu
 * @param array $history Tarixçə massivi
 * @return bool Uğurlu saxlanıb-saxlanmadığı
 */
function saveHistoryToFile($filePath, $history) {
    // Məlumatları JSON formatında kodla
    $jsonData = json_encode($history, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    
    if ($jsonData === false) {
        error_log("Tarixçə JSON encode xətası: " . json_last_error_msg());
        return false;
    }
    
    // Faylı yaz
    $result = file_put_contents($filePath, $jsonData, LOCK_EX);
    
    if ($result === false) {
        error_log("Tarixçə faylı yazıla bilmədi: " . $filePath);
        return false;
    }
    
    return true;
}

/**
 * Boş tarixçə faylı yaradır
 * 
 * @param string $filePath Fayl yolu
 * @return bool Uğurlu yaradılıb-yaradılmadığı
 */
function createEmptyHistoryFile($filePath) {
    // Qovluğu yarat (əgər yoxdursa)
    $directory = dirname($filePath);
    if (!is_dir($directory)) {
        if (!mkdir($directory, 0755, true)) {
            error_log("Tarixçə qovluğu yaradıla bilmədi: " . $directory);
            return false;
        }
    }
    
    return saveHistoryToFile($filePath, []);
}

/**
 * Tarixçəni təmizləyir
 * 
 * @return bool Uğurlu təmizlənib-təmizlənmədiyi
 */
function clearGlobalHistory() {
    $historyFile = __DIR__ . '/../global_search_history.json';
    return saveHistoryToFile($historyFile, []);
}

/**
 * Tarixçə statistikalarını qaytarır
 * 
 * @return array Statistika məlumatları
 */
function getHistoryStatistics() {
    $history = getGlobalHistory(100); // Daha çox məlumat üçün
    
    if (empty($history)) {
        return [
            'total_searches' => 0,
            'unique_terms' => 0,
            'most_searched' => null,
            'recent_activity' => []
        ];
    }
    
    // Unikal terminləri say
    $uniqueTerms = array_unique(array_map('normalize', $history));
    
    // Ən çox axtarılan termini tap
    $termCounts = array_count_values(array_map('normalize', $history));
    arsort($termCounts);
    $mostSearched = array_key_first($termCounts);
    
    // Son aktivlik (son 5 axtarış)
    $recentActivity = array_slice($history, 0, 5);
    
    return [
        'total_searches' => count($history),
        'unique_terms' => count($uniqueTerms),
        'most_searched' => [
            'term' => $mostSearched,
            'count' => $termCounts[$mostSearched] ?? 0
        ],
        'recent_activity' => $recentActivity
    ];
}

/**
 * Tarixçədə termin axtarır
 * 
 * @param string $searchTerm Axtarılacaq termin
 * @return array Uyğun gələn tarixçə elementləri
 */
function searchInHistory($searchTerm) {
    $history = getGlobalHistory(50);
    $normalizedSearch = normalize($searchTerm);
    
    $matches = [];
    
    foreach ($history as $index => $term) {
        $normalizedTerm = normalize($term);
        
        if (strpos($normalizedTerm, $normalizedSearch) !== false) {
            $matches[] = [
                'term' => $term,
                'position' => $index,
                'score' => calculateSearchScore($searchTerm, $term, $term)
            ];
        }
    }
    
    // Score-a görə sırala
    usort($matches, function($a, $b) {
        return $b['score'] - $a['score'];
    });
    
    return $matches;
}
