<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST');
header('Access-Control-Allow-Headers: Content-Type');

$historyFile = 'global_search_history.json';
$maxHistoryItems = 10;

// Global axtarış tarixçəsini oxu
function getGlobalHistory() {
    global $historyFile;
    
    if (!file_exists($historyFile)) {
        return [];
    }
    
    $content = file_get_contents($historyFile);
    $data = json_decode($content, true);
    
    return is_array($data) ? $data : [];
}

// Global axtarış tarixçəsinə əlavə et
function addToGlobalHistory($searchTerm) {
    global $historyFile, $maxHistoryItems;
    
    $searchTerm = trim($searchTerm);
    if (empty($searchTerm) || strlen($searchTerm) < 2) {
        return false;
    }
    
    $history = getGlobalHistory();
    
    // Normallaşdırma funksiyası
    function normalize($text) {
        $text = mb_strtolower($text, 'UTF-8');
        $replacements = [
            'ə' => 'e', 'ı' => 'i', 'ö' => 'o', 'ü' => 'u', 'ç' => 'c', 'ş' => 's', 'ğ' => 'g',
            'Ə' => 'e', 'I' => 'i', 'Ö' => 'o', 'Ü' => 'u', 'Ç' => 'c', 'Ş' => 's', 'Ğ' => 'g'
        ];
        return strtr($text, $replacements);
    }
    
    $normalizedSearch = normalize($searchTerm);
    
    // Mövcud olanı sil (case-insensitive)
    $history = array_filter($history, function($item) use ($normalizedSearch) {
        return normalize($item) !== $normalizedSearch;
    });
    
    // Başa əlavə et
    array_unshift($history, $searchTerm);
    
    // Maksimum sayda saxla
    if (count($history) > $maxHistoryItems) {
        $history = array_slice($history, 0, $maxHistoryItems);
    }
    
    // Faylı yenilə
    file_put_contents($historyFile, json_encode($history, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT));
    
    return true;
}

// HTTP metoduna görə əməliyyat
$method = $_SERVER['REQUEST_METHOD'];

if ($method === 'GET') {
    // Tarixçəni qaytır
    echo json_encode([
        'success' => true,
        'history' => getGlobalHistory()
    ]);
    
} elseif ($method === 'POST') {
    // Yeni axtarış əlavə et
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!isset($input['searchTerm'])) {
        echo json_encode([
            'success' => false,
            'error' => 'searchTerm parametri tələb olunur'
        ]);
        exit;
    }
    
    $success = addToGlobalHistory($input['searchTerm']);
    
    echo json_encode([
        'success' => $success,
        'history' => getGlobalHistory()
    ]);
    
} else {
    echo json_encode([
        'success' => false,
        'error' => 'Dəstəklənməyən HTTP metodu'
    ]);
}
?>
