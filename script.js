let allData = [];
let isLoading = false;

// JSO<PERSON> faylını yüklə
async function loadData() {
    if (isLoading) return;

    isLoading = true;
    const loadingIndicator = document.getElementById('loadingIndicator');
    if (loadingIndicator) {
        loadingIndicator.style.display = 'block';
    }

    try {
        const res = await fetch('data.json');
        if (!res.ok) {
            throw new Error(`HTTP error! status: ${res.status}`);
        }
        allData = await res.json();

        // Məlumatları yükləndikdən sonra cache-ə saxla
        if ('localStorage' in window) {
            localStorage.setItem('drugData', JSON.stringify(allData));
            localStorage.setItem('drugDataTimestamp', Date.now().toString());
        }

        console.log(`${allData.length} dərman məlumatı yükləndi`);
    } catch (e) {
        console.error('Məlumat yüklənərkən xəta:', e);

        // Cache-dən yükləməyə çalış
        if ('localStorage' in window) {
            const cachedData = localStorage.getItem('drugData');
            if (cachedData) {
                allData = JSON.parse(cachedData);
                showNotification("Məlumat cache-dən yükləndi", "warning");
                return;
            }
        }

        showNotification("Məlumat yüklənərkən xəta baş verdi: " + e.message, "error");
    } finally {
        isLoading = false;
        if (loadingIndicator) {
            loadingIndicator.style.display = 'none';
        }
    }
}

// Bildiriş göstərmə funksiyası
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;

    // Notification container yarat (yoxdursa)
    let container = document.getElementById('notificationContainer');
    if (!container) {
        container = document.createElement('div');
        container.id = 'notificationContainer';
        container.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            max-width: 300px;
        `;
        document.body.appendChild(container);
    }

    container.appendChild(notification);

    // 3 saniyə sonra sil
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 3000);
}

// Normalizasiya funksiyası - təkmilləşdirilmiş
function normalize(str) {
    if (!str) return '';

    return str
        .toLowerCase()
        .trim()
        .replace(/c/g, 's')
        .replace(/x/g, 'ch')
        .replace(/ş/g, 's')
        .replace(/ə/g, 'e')
        .replace(/ı/g, 'i')
        .replace(/ü/g, 'u')
        .replace(/ö/g, 'o')
        .replace(/ğ/g, 'g')
        .replace(/s/g, 'c')
        .replace(/k/g, 'c')
        .replace(/ç/g, 'c')
        .replace(/w/g, 'v')
        .replace(/y/g, 'i')
        .replace(/\s+/g, ' '); // Çoxlu boşluqları tək boşluğa çevir
}

// Debounce funksiyası - çox tez-tez axtarışı məhdudlaşdırır
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Təkliflər üçün çoxlu bileşik axtarış dəstəyi
function getUniqueTesirediciSuggestions(query, limit = 10) {
    if (!query || query.length < 2) return [];

    // Son yazılan hissəni al (vergül və ya + işarətindən sonra)
    const lastPart = query.split(/[,+]/).pop().trim();
    const normQuery = normalize(lastPart);

    if (normQuery.length < 2) return [];

    const seen = new Set();

    // Prioritet: tam uyğunluq > başlanğıc uyğunluğu > komponent uyğunluğu > daxili uyğunluq
    const exactMatches = [];
    const startMatches = [];
    const componentMatches = [];
    const containsMatches = [];

    allData.forEach(item => {
        if (!item.tesiredici) return;

        const normTesiredici = normalize(item.tesiredici);
        const drugComponents = analyzeDrugComponents(item.tesiredici);

        if (seen.has(normTesiredici)) return;

        // Tam uyğunluq
        if (normTesiredici === normQuery) {
            exactMatches.push(item);
            seen.add(normTesiredici);
        }
        // Başlanğıc uyğunluğu
        else if (normTesiredici.startsWith(normQuery)) {
            startMatches.push(item);
            seen.add(normTesiredici);
        }
        // Komponent uyğunluğu (dərmanın komponentlərindən biri uyğun gəlir)
        else if (drugComponents.some(comp => comp.startsWith(normQuery) || comp === normQuery)) {
            componentMatches.push(item);
            seen.add(normTesiredici);
        }
        // Daxili uyğunluq
        else if (normTesiredici.includes(normQuery)) {
            containsMatches.push(item);
            seen.add(normTesiredici);
        }
    });

    // Prioritet sırası ilə birləşdir
    const result = [...exactMatches, ...startMatches, ...componentMatches, ...containsMatches];
    return result.slice(0, limit);
}

// Çoxlu bileşik arama sistemi
function parseSearchQuery(query) {
    if (!query) return [];

    // Vergüllə ayrılmış sorğuları işlə
    const mainQueries = query.split(',').map(q => q.trim()).filter(q => q.length > 0);
    const allSearchTerms = [];

    mainQueries.forEach(mainQuery => {
        const normalizedMain = normalize(mainQuery);

        // "+" işarəti varsa, kombinasiya axtarışı
        if (mainQuery.includes('+')) {
            const components = mainQuery.split('+').map(c => normalize(c.trim())).filter(c => c.length > 0);

            if (components.length > 1) {
                // Kombinasiya axtarışı əlavə et
                allSearchTerms.push({
                    type: 'combination',
                    components: components,
                    original: mainQuery
                });

                // Hər bir komponenti ayrıca da əlavə et
                components.forEach(component => {
                    if (!allSearchTerms.some(term =>
                        term.type === 'single' && term.term === component
                    )) {
                        allSearchTerms.push({
                            type: 'single',
                            term: component,
                            original: component
                        });
                    }
                });
            }
        } else {
            // Sadə axtarış
            allSearchTerms.push({
                type: 'single',
                term: normalizedMain,
                original: mainQuery
            });
        }
    });

    return allSearchTerms;
}

// Dərmanın təsiredici maddələrini analiz et
function analyzeDrugComponents(tesiredici) {
    if (!tesiredici) return [];

    const normalized = normalize(tesiredici);

    // Müxtəlif ayırıcıları yoxla
    const separators = ['+', '/', '&', ' and ', ' və ', ',', ';'];
    let components = [normalized];

    separators.forEach(sep => {
        const newComponents = [];
        components.forEach(comp => {
            if (comp.includes(sep)) {
                newComponents.push(...comp.split(sep).map(c => c.trim()).filter(c => c.length > 0));
            } else {
                newComponents.push(comp);
            }
        });
        components = newComponents;
    });

    return components.filter(c => c.length > 1); // Çox qısa komponentləri çıxar
}

// Nəticələr üçün təkmilləşdirilmiş axtarış
function filterByTesiredici(query) {
    if (!query) return [];

    const searchTerms = parseSearchQuery(query);
    if (searchTerms.length === 0) return [];

    const results = new Set();

    allData.forEach(item => {
        if (!item.tesiredici || !item.terkib) return;

        const drugComponents = analyzeDrugComponents(item.tesiredici);
        const normalizedTesiredici = normalize(item.tesiredici);

        // Hər bir axtarış termini üçün yoxla
        searchTerms.forEach(searchTerm => {
            let matches = false;

            if (searchTerm.type === 'single') {
                // Sadə axtarış - tam uyğunluq və ya komponent uyğunluğu
                if (normalizedTesiredici === searchTerm.term ||
                    drugComponents.includes(searchTerm.term)) {
                    matches = true;
                }
            } else if (searchTerm.type === 'combination') {
                // Kombinasiya axtarışı - bütün komponentlər mövcud olmalıdır
                const allComponentsPresent = searchTerm.components.every(component =>
                    drugComponents.includes(component) || normalizedTesiredici.includes(component)
                );

                if (allComponentsPresent) {
                    matches = true;
                }
            }

            if (matches) {
                results.add(item);
            }
        });
    });

    // Nəticələri massivə çevir və sırala
    const finalResults = Array.from(results);

    return finalResults.sort((a, b) => {
        // Əvvəlcə kombinasiya dərmanları, sonra tək maddəli dərmanlar
        const aComponents = analyzeDrugComponents(a.tesiredici).length;
        const bComponents = analyzeDrugComponents(b.tesiredici).length;

        if (aComponents !== bComponents) {
            return bComponents - aComponents; // Çox komponentli əvvəl
        }

        // Sonra əlifba sırası
        const nameA = a.terkib || '';
        const nameB = b.terkib || '';
        return nameA.localeCompare(nameB, 'az');
    });
}

// Axtarış tarixçəsi
class SearchHistory {
    constructor() {
        this.maxItems = 10;
        this.storageKey = 'drugSearchHistory';
        this.history = this.loadHistory();
    }

    loadHistory() {
        try {
            const stored = localStorage.getItem(this.storageKey);
            return stored ? JSON.parse(stored) : [];
        } catch (e) {
            console.error('Tarixçə yüklənərkən xəta:', e);
            return [];
        }
    }

    saveHistory() {
        try {
            localStorage.setItem(this.storageKey, JSON.stringify(this.history));
        } catch (e) {
            console.error('Tarixçə saxlanılarkən xəta:', e);
        }
    }

    addSearch(query) {
        if (!query || query.trim().length < 2) return;

        const normalizedQuery = normalize(query.trim());

        // Mövcud olanı sil
        this.history = this.history.filter(item =>
            normalize(item) !== normalizedQuery
        );

        // Başa əlavə et
        this.history.unshift(query.trim());

        // Maksimum sayda saxla
        if (this.history.length > this.maxItems) {
            this.history = this.history.slice(0, this.maxItems);
        }

        this.saveHistory();

        // Nümunələri yenilə
        updateSearchExamples();
    }

    getHistory() {
        return [...this.history];
    }

    clearHistory() {
        this.history = [];
        this.saveHistory();

        // Nümunələri yenilə
        updateSearchExamples();
    }
}

const searchHistory = new SearchHistory();

// Son axtarılanları göstərmə funksiyası
function updateSearchExamples() {
    const examplesContainer = document.getElementById('examplesContainer');
    const examplesLabel = document.getElementById('examplesLabel');

    if (!examplesContainer || !examplesLabel) return;

    const history = searchHistory.getHistory();

    if (history.length === 0) {
        examplesLabel.textContent = 'Nümunələr:';
        examplesContainer.innerHTML = `
            <a href="#" onclick="searchExample('paracetamol')">paracetamol</a>,
            <a href="#" onclick="searchExample('ibuprofen')">ibuprofen</a>,
            <a href="#" onclick="searchExample('aspirin')">aspirin</a>
        `;
        return;
    }

    examplesLabel.textContent = 'Son axtarılanlar:';

    // Son 10 axtarışı göstər
    const recentSearches = history.slice(0, 10);
    const links = recentSearches.map(term =>
        `<a href="#" onclick="searchExample('${term.replace(/'/g, "\\'")}')">${term}</a>`
    ).join(', ');

    examplesContainer.innerHTML = links;
}




// Tövsiyələri göstər - tag sistemi ilə
function showSuggestions(matches) {
    const suggestions = document.getElementById('suggestions');
    suggestions.innerHTML = '';

    if (matches.length === 0) {
        suggestions.style.display = 'none';
        return;
    }

    matches.forEach((item, index) => {
        const div = document.createElement('div');
        div.className = 'suggestion-item';

        // Highlight matching text
        const query = document.getElementById('searchBox').value.trim();
        const highlightedText = highlightMatch(item.tesiredici, query);
        div.innerHTML = highlightedText;

        div.addEventListener('click', () => {
            // Tag əlavə et və axtarış et
            addTagAndSearch(item.tesiredici);
            document.getElementById('searchBox').value = '';
            suggestions.style.display = 'none';
        });

        // Klaviatura naviqasiyası üçün
        div.dataset.index = index;
        suggestions.appendChild(div);
    });

    suggestions.style.display = 'block';
}

// Uyğun mətnləri vurğula
function highlightMatch(text, query) {
    if (!query) return text;

    const normalizedQuery = normalize(query);
    const normalizedText = normalize(text);

    const index = normalizedText.indexOf(normalizedQuery);
    if (index === -1) return text;

    // Orijinal mətndə uyğun hissəni tap
    const before = text.substring(0, index);
    const match = text.substring(index, index + query.length);
    const after = text.substring(index + query.length);

    return `${before}<mark>${match}</mark>${after}`;
}

// Nəticələri göstər - tag sistemi ilə
function renderResults(query) {
    const container = document.getElementById('resultContainer');
    container.innerHTML = '';
    container.classList.remove('no-results');

    // Axtarışı tarixçəyə əlavə et
    if (query && query.trim()) {
        searchHistory.addSearch(query.trim());
    }

    const filtered = filterByTesiredici(query);

    if (filtered.length === 0) {
        const noResultsDiv = document.createElement('div');
        noResultsDiv.className = 'no-results-message';
        noResultsDiv.innerHTML = `
            <h3>Uyğun dərman tapılmadı</h3>
            <p>Seçilmiş tərkiblər: "<strong>${selectedTags.join(', ')}</strong>"</p>
            <p>Tövsiyələr:</p>
            <ul>
                <li>Bəzi tag-ları silin və yenidən cəhd edin</li>
                <li>Yazılışı yoxlayın</li>
                <li>Daha az tərkib seçin</li>
                <li>Müxtəlif sinonimləri sınayın</li>
            </ul>
        `;
        container.appendChild(noResultsDiv);
        container.classList.add('no-results');
        return;
    }

    // Nəticə sayını göstər
    const resultInfo = document.createElement('div');
    resultInfo.className = 'result-count';

    let infoText = `${filtered.length} dərman tapıldı`;
    if (selectedTags.length > 1) {
        infoText += ` (${selectedTags.length} tərkib kombinasiyası)`;
    }

    resultInfo.textContent = infoText;
    container.appendChild(resultInfo);

    // Seçilmiş tag-lar haqqında məlumat
    if (selectedTags.length > 0) {
        const tagInfo = document.createElement('div');
        tagInfo.className = 'search-info';
        tagInfo.innerHTML = `
            <strong>Seçilmiş tərkiblər:</strong><br>
            ${selectedTags.map(tag => `🏷️ ${tag}`).join('<br>')}
        `;
        container.appendChild(tagInfo);
    }

    const table = document.createElement('table');
    table.id = "resultTable";

    const thead = document.createElement('thead');
    const headerRow = document.createElement('tr');

    const th1 = document.createElement('th');
    th1.textContent = "Seç";

    const th2 = document.createElement('th');
    th2.style.display = 'flex';
    th2.style.justifyContent = 'space-between';
    th2.style.alignItems = 'center';

    const titleSpan = document.createElement('span');
    titleSpan.textContent = "Dərman Adı (Tərkib)";

    // Kopyalama düymələri konteyner
    const buttonContainer = document.createElement('div');
    buttonContainer.className = 'copy-buttons';

    const selectAllBtn = document.createElement('button');
    selectAllBtn.className = 'select-all-btn';
    selectAllBtn.textContent = "Hamısını Seç";
    selectAllBtn.addEventListener('click', () => {
        const checkboxes = document.querySelectorAll('input[type="checkbox"].drug-check');
        const allChecked = Array.from(checkboxes).every(cb => cb.checked);
        checkboxes.forEach(cb => cb.checked = !allChecked);
        selectAllBtn.textContent = allChecked ? "Hamısını Seç" : "Hamısını Ləğv Et";
    });

    const copyAllBtn = document.createElement('button');
    copyAllBtn.className = 'copy-all-btn';
    copyAllBtn.textContent = "Seçilənləri Kopyala";

    copyAllBtn.addEventListener('click', async () => {
        try {
            const checkboxes = document.querySelectorAll('input[type="checkbox"].drug-check');
            const selectedItems = [];

            checkboxes.forEach(checkbox => {
                if (checkbox.checked) {
                    selectedItems.push(checkbox.dataset.terkib);
                }
            });

            if (selectedItems.length === 0) {
                showNotification("Zəhmət olmasa ən azı bir dərmanı seçin.", "warning");
                return;
            }

            let htmlTable = `<table border="1" cellspacing="0" cellpadding="6" style="border-collapse:collapse; font-family: Arial, sans-serif; font-size: 14px;">`;
            htmlTable += `<tbody>`;

            selectedItems.forEach(terkib => {
                htmlTable += `<tr><td>${terkib}</td></tr>`;
            });

            htmlTable += `</tbody></table>`;

            await navigator.clipboard.write([
                new ClipboardItem({
                    "text/html": new Blob([htmlTable], { type: "text/html" }),
                    "text/plain": new Blob([selectedItems.join('\n')], { type: "text/plain" })
                })
            ]);

            copyAllBtn.textContent = "Kopyalandı!";
            showNotification(`${selectedItems.length} dərman kopyalandı`, "success");
            setTimeout(() => (copyAllBtn.textContent = "Seçilənləri Kopyala"), 1500);
        } catch (err) {
            console.error('Kopyalama xətası:', err);
            showNotification("Kopyalama mümkün olmadı: " + err.message, "error");
        }
    });

    buttonContainer.appendChild(selectAllBtn);
    buttonContainer.appendChild(copyAllBtn);

    th2.appendChild(titleSpan);
    th2.appendChild(buttonContainer);
    headerRow.appendChild(th1);
    headerRow.appendChild(th2);
    thead.appendChild(headerRow);
    table.appendChild(thead);

    const tbody = document.createElement('tbody');
    filtered.forEach(item => {
        const tr = document.createElement('tr');

        const checkboxTd = document.createElement('td');
        const checkbox = document.createElement('input');
        checkbox.type = 'checkbox';
        checkbox.checked = true;
        checkbox.className = 'drug-check';
        checkbox.dataset.terkib = item.terkib;
        checkboxTd.appendChild(checkbox);

        const dataTd = document.createElement('td');
        // Görünəndə dərman adı + (maddə) yazılsın
        dataTd.textContent = `${item.terkib} (${item.tesiredici})`;

        // Amma checkbox üçün sadəcə dərman adı getsin
        checkbox.dataset.terkib = item.terkib;
        
        tr.appendChild(checkboxTd);
        tr.appendChild(dataTd);
        tbody.appendChild(tr);
    });

    table.appendChild(tbody);
    container.appendChild(table);
}

// Klaviatura naviqasiyası və tag sistemi
let currentSuggestionIndex = -1;

function handleKeyboardNavigation(e) {
    const suggestions = document.getElementById('suggestions');
    const suggestionItems = suggestions.querySelectorAll('.suggestion-item');

    switch (e.key) {
        case 'ArrowDown':
            e.preventDefault();
            if (suggestionItems.length > 0) {
                currentSuggestionIndex = Math.min(currentSuggestionIndex + 1, suggestionItems.length - 1);
                updateSuggestionHighlight(suggestionItems);
            }
            break;
        case 'ArrowUp':
            e.preventDefault();
            if (suggestionItems.length > 0) {
                currentSuggestionIndex = Math.max(currentSuggestionIndex - 1, -1);
                updateSuggestionHighlight(suggestionItems);
            }
            break;
        case 'Enter':
            e.preventDefault();
            if (currentSuggestionIndex >= 0 && currentSuggestionIndex < suggestionItems.length) {
                // Təklif seçildi
                suggestionItems[currentSuggestionIndex].click();
            } else {
                // Input-dan birbaşa tag əlavə et
                const val = e.target.value.trim();
                if (val !== '') {
                    addTagAndSearch(val);
                    e.target.value = '';
                    suggestions.style.display = 'none';
                }
            }
            currentSuggestionIndex = -1;
            break;
        case 'Escape':
            suggestions.style.display = 'none';
            currentSuggestionIndex = -1;
            break;
    }
}

function updateSuggestionHighlight(items) {
    items.forEach((item, index) => {
        item.classList.toggle('highlighted', index === currentSuggestionIndex);
    });
}

// Debounced axtarış funksiyası
const debouncedSearch = debounce((query) => {
    if (query.length >= 2) {
        const matches = getUniqueTesirediciSuggestions(query);
        showSuggestions(matches);
    } else {
        document.getElementById('suggestions').style.display = 'none';
    }
    currentSuggestionIndex = -1;
}, 300);

window.addEventListener('DOMContentLoaded', async () => {
    // Loading indicator əlavə et
    const loadingDiv = document.createElement('div');
    loadingDiv.id = 'loadingIndicator';
    loadingDiv.innerHTML = '<div class="spinner"></div><span>Məlumatlar yüklənir...</span>';
    loadingDiv.style.display = 'none';
    document.body.appendChild(loadingDiv);

    await loadData();

    // Səhifə yüklənəndə nümunələri göstər
    updateSearchExamples();

    // "Hamısını sil" düyməsini aktivləşdir
    const clearAllBtn = document.getElementById('clearAllTags');
    if (clearAllBtn) {
        clearAllBtn.addEventListener('click', clearAllTags);
    }

    const searchBox = document.getElementById('searchBox');
    const suggestions = document.getElementById('suggestions');

    // Focus olduqda heç nə göstərmə
    searchBox.addEventListener('focus', () => {
        if (searchBox.value.trim() === '') {
            suggestions.style.display = 'none';
        }
    });

    searchBox.addEventListener('input', (e) => {
        const val = e.target.value.trim();
        if (val === '') {
            document.getElementById('resultContainer').innerHTML = '';
        }
        debouncedSearch(val);
    });

    searchBox.addEventListener('keydown', handleKeyboardNavigation);

    document.addEventListener('click', (e) => {
        if (!e.target.closest('.search-wrapper')) {
            suggestions.style.display = 'none';
            currentSuggestionIndex = -1;
        }
    });

    // Tarixçəni təmizləmə düyməsi əlavə et
    const clearHistoryBtn = document.createElement('button');
    clearHistoryBtn.id = 'clearHistoryBtn';
    clearHistoryBtn.textContent = 'Tarixçəni Təmizlə';
    clearHistoryBtn.style.display = 'none';
    clearHistoryBtn.addEventListener('click', () => {
        searchHistory.clearHistory();
        showNotification('Axtarış tarixçəsi təmizləndi', 'info');
        suggestions.style.display = 'none';
    });

    // Search wrapper-ə əlavə et
    const searchWrapper = document.querySelector('.search-wrapper');
    if (searchWrapper) {
        searchWrapper.appendChild(clearHistoryBtn);
    }
});

// Tag sistemi - təkmilləşdirilmiş
let selectedTags = [];

function updateTagsUI() {
    const container = document.getElementById('selectedTagsContainer');
    const tagsDiv = document.getElementById('selectedTags');

    if (!container || !tagsDiv) return;

    if (selectedTags.length === 0) {
        container.style.display = 'none';
        return;
    }

    container.style.display = 'block';
    tagsDiv.innerHTML = '';

    selectedTags.forEach((tag, index) => {
        const tagElement = document.createElement('div');
        tagElement.className = 'tag-item';
        tagElement.innerHTML = `
            <span class="tag-text">${tag}</span>
            <button class="tag-remove" onclick="removeTag(${index})" title="Sil">×</button>
        `;
        tagsDiv.appendChild(tagElement);
    });
}

// Tag silmə funksiyası
function removeTag(index) {
    if (index >= 0 && index < selectedTags.length) {
        const removedTag = selectedTags.splice(index, 1)[0];
        updateTagsUI();

        // Əgər tag-lar varsa, yenidən axtarış et
        if (selectedTags.length > 0) {
            const combinedQuery = selectedTags.join(',');
            renderResults(combinedQuery);
        } else {
            // Bütün tag-lar silinibsə, nəticələri təmizlə
            resetToInitialState();
        }

        showNotification(`"${removedTag}" silindi`, "info");
    }
}

// İlkin vəziyyətə qaytar
function resetToInitialState() {
    document.getElementById('resultContainer').innerHTML = `
        <div class="initial-info">
            <h2>Dərman Axtarış Sistemi</h2>
            <p>Yuxarıdakı axtarış qutusuna təsiredici maddənin adını yazaraq dərmanları tapa bilərsiniz.</p>
            <p>Enter basaraq tag əlavə edin və çoxlu tərkib axtarışı edin.</p>

            <div class="search-tips">
                <h3>Tag Sistemi:</h3>
                <ul>
                    <li><strong>1. Addım:</strong> <code>PARACETAMOL</code> yazın və Enter basın</li>
                    <li><strong>2. Addım:</strong> <code>IBUPROFEN</code> yazın və Enter basın</li>
                    <li><strong>Nəticə:</strong> Həm PARACETAMOL həm də IBUPROFEN olan dərmanlar</li>
                </ul>
                <p><em>Hər Enter basışında yeni tag əlavə olur və kombinasiya axtarışı edilir.</em></p>
            </div>
        </div>
    `;
}

// Tag əlavə etmə və axtarış funksiyası
function addTagAndSearch(tag) {
    const normalizedTag = tag.trim();
    if (!normalizedTag) return false;

    // Əgər tag artıq varsa, əlavə etmə
    if (selectedTags.includes(normalizedTag)) {
        showNotification(`"${normalizedTag}" artıq əlavə edilib`, "warning");
        return false;
    }

    selectedTags.push(normalizedTag);
    updateTagsUI();

    // Bütün tag-ları birləşdirərək axtarış et
    const combinedQuery = selectedTags.join(',');
    renderResults(combinedQuery);

    showNotification(`"${normalizedTag}" əlavə edildi`, "success");
    return true;
}

// Tag əlavə etmə funksiyası (köhnə)
function addTag(tag) {
    return addTagAndSearch(tag);
}

// Bütün tag-ları təmizlə
function clearAllTags() {
    selectedTags = [];
    updateTagsUI();
    resetToInitialState();
    showNotification("Bütün tag-lar silindi", "info");
}

// Export/Import funksiyaları
function exportResults() {
    const checkboxes = document.querySelectorAll('input[type="checkbox"].drug-check:checked');
    const selectedDrugs = Array.from(checkboxes).map(cb => cb.dataset.terkib);

    if (selectedDrugs.length === 0) {
        showNotification('Eksport üçün dərman seçin', 'warning');
        return;
    }

    const data = {
        timestamp: new Date().toISOString(),
        query: selectedTags.join(','),
        drugs: selectedDrugs,
        count: selectedDrugs.length
    };

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `derman_siyahisi_${new Date().toISOString().split('T')[0]}.json`;
    a.click();
    URL.revokeObjectURL(url);

    showNotification(`${selectedDrugs.length} dərman eksport edildi`, 'success');
}
