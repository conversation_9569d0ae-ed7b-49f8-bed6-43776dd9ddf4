// <PERSON><PERSON><PERSON> məlumatları və axtarış funksiyaları
let allData = [];
let isLoading = false;
let searchHistory = new SearchHistory();

// Tema yükləmə
function loadTheme() {
    const savedTheme = localStorage.getItem('theme') || 'light';
    document.documentElement.setAttribute('data-theme', savedTheme);

    const themeIcon = document.querySelector('#themeToggle i');
    if (themeIcon) {
        themeIcon.className = savedTheme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
    }
}

// Tema dəyişdirmə funksiyası
function toggleTheme() {
    const currentTheme = document.documentElement.getAttribute('data-theme');
    const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

    document.documentElement.setAttribute('data-theme', newTheme);
    localStorage.setItem('theme', newTheme);

    // İkonu yenilə
    const themeIcon = document.querySelector('#themeToggle i');
    if (themeIcon) {
        themeIcon.className = newTheme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
    }

    showNotification(`${newTheme === 'dark' ? 'Qaranlıq' : 'İşıqlı'} tema aktivləşdirildi`, 'success');
}

// Yükləmə ekranını gizlət və əsas tətbiqi göstər
function hideLoadingScreen() {
    const loadingScreen = document.getElementById('loadingScreen');
    const mainApp = document.getElementById('mainApp');

    if (loadingScreen && mainApp) {
        loadingScreen.style.opacity = '0';
        loadingScreen.style.visibility = 'hidden';
        mainApp.style.display = 'flex';

        setTimeout(() => {
            loadingScreen.style.display = 'none';
        }, 350);
    }
}

// JSON faylını yüklə
async function loadData() {
    if (isLoading) return;

    isLoading = true;

    try {
        const res = await fetch('data.json');
        if (!res.ok) {
            throw new Error(`HTTP error! status: ${res.status}`);
        }
        allData = await res.json();

        // Məlumatları yükləndikdən sonra cache-ə saxla
        if ('localStorage' in window) {
            localStorage.setItem('drugData', JSON.stringify(allData));
            localStorage.setItem('drugDataTimestamp', Date.now().toString());
        }

        // Statistikaları yenilə
        updateSearchStats();

        console.log(`${allData.length} dərman məlumatı yükləndi`);

        // Yükləmə tamamlandıqdan sonra ana tətbiqi göstər
        hideLoadingScreen();

    } catch (e) {
        console.error('Məlumat yüklənərkən xəta:', e);

        // Cache-dən yükləməyə çalış
        if ('localStorage' in window) {
            const cachedData = localStorage.getItem('drugData');
            if (cachedData) {
                allData = JSON.parse(cachedData);
                updateSearchStats();
                hideLoadingScreen();
                showNotification("Məlumat cache-dən yükləndi", "warning");
                return;
            }
        }

        hideLoadingScreen();
        showNotification("Məlumat yüklənərkən xəta baş verdi: " + e.message, "error");
    } finally {
        isLoading = false;
    }
}

// Axtarış statistikalarını yenilə
function updateSearchStats() {
    const totalDrugsElement = document.getElementById('totalDrugs');
    if (totalDrugsElement && allData.length > 0) {
        totalDrugsElement.textContent = `${allData.length} dərman yükləndi`;
    }
}

// Bildiriş göstərmə funksiyası
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;

    // Notification container yarat (yoxdursa)
    let container = document.getElementById('notificationContainer');
    if (!container) {
        container = document.createElement('div');
        container.id = 'notificationContainer';
        container.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            max-width: 300px;
        `;
        document.body.appendChild(container);
    }

    container.appendChild(notification);

    // 3 saniyə sonra sil
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 3000);
}

// Normalizasiya funksiyası - təkmilləşdirilmiş
function normalize(str) {
    if (!str) return '';

    return str
        .toLowerCase()
        .trim()
        .replace(/c/g, 's')
        .replace(/x/g, 'ch')
        .replace(/ş/g, 's')
        .replace(/ə/g, 'e')
        .replace(/ı/g, 'i')
        .replace(/ü/g, 'u')
        .replace(/ö/g, 'o')
        .replace(/ğ/g, 'g')
        .replace(/s/g, 'c')
        .replace(/k/g, 'c')
        .replace(/ç/g, 'c')
        .replace(/w/g, 'v')
        .replace(/y/g, 'i')
        .replace(/\s+/g, ' '); // Çoxlu boşluqları tək boşluğa çevir
}

// Debounce funksiyası - çox tez-tez axtarışı məhdudlaşdırır
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Təkliflər üçün unikal tesiredici - təkmilləşdirilmiş
function getUniqueTesirediciSuggestions(query, limit = 10) {
    if (!query || query.length < 2) return [];

    const normQuery = normalize(query);
    const seen = new Set();

    // Prioritet: tam uyğunluq > başlanğıc uyğunluğu > daxili uyğunluq
    const exactMatches = [];
    const startMatches = [];
    const containsMatches = [];

    allData.forEach(item => {
        if (!item.tesiredici) return;

        const normTesiredici = normalize(item.tesiredici);

        if (seen.has(normTesiredici)) return;

        if (normTesiredici === normQuery) {
            exactMatches.push(item);
            seen.add(normTesiredici);
        } else if (normTesiredici.startsWith(normQuery)) {
            startMatches.push(item);
            seen.add(normTesiredici);
        } else if (normTesiredici.includes(normQuery)) {
            containsMatches.push(item);
            seen.add(normTesiredici);
        }
    });

    // Prioritet sırası ilə birləşdir
    const result = [...exactMatches, ...startMatches, ...containsMatches];
    return result.slice(0, limit);
}

// Nəticələr üçün tam uyğun dərman siyahısı - təkmilləşdirilmiş
function filterByTesiredici(query) {
    if (!query) return [];

    // Bir neçə maddəni vergüllə ayırıb normallaşdırırıq
    const queries = query
        .split(',')
        .map(q => normalize(q))
        .filter(q => q.length > 0);

    if (queries.length === 0) return [];

    const results = allData.filter(item => {
        if (!item.tesiredici || !item.terkib) return false;

        const normalizedTesiredici = normalize(item.tesiredici);

        // Əgər hər hansısa bir sorğu tam uyğun gəlirsə, daxil et
        return queries.some(q => normalizedTesiredici === q);
    });

    // Nəticələri əlifba sırası ilə sırala
    return results.sort((a, b) => {
        const nameA = a.terkib || '';
        const nameB = b.terkib || '';
        return nameA.localeCompare(nameB, 'az');
    });
}

// Axtarış tarixçəsi
class SearchHistory {
    constructor() {
        this.maxItems = 10;
        this.storageKey = 'drugSearchHistory';
        this.history = this.loadHistory();
    }

    loadHistory() {
        try {
            const stored = localStorage.getItem(this.storageKey);
            return stored ? JSON.parse(stored) : [];
        } catch (e) {
            console.error('Tarixçə yüklənərkən xəta:', e);
            return [];
        }
    }

    saveHistory() {
        try {
            localStorage.setItem(this.storageKey, JSON.stringify(this.history));
        } catch (e) {
            console.error('Tarixçə saxlanılarkən xəta:', e);
        }
    }

    addSearch(query) {
        if (!query || query.trim().length < 2) return;

        const normalizedQuery = normalize(query.trim());

        // Mövcud olanı sil
        this.history = this.history.filter(item =>
            normalize(item) !== normalizedQuery
        );

        // Başa əlavə et
        this.history.unshift(query.trim());

        // Maksimum sayda saxla
        if (this.history.length > this.maxItems) {
            this.history = this.history.slice(0, this.maxItems);
        }

        this.saveHistory();
    }

    getHistory() {
        return [...this.history];
    }

    clearHistory() {
        this.history = [];
        this.saveHistory();
    }
}

// SearchHistory artıq yuxarıda təyin edilib




// Tövsiyələri göstər - təkmilləşdirilmiş
function showSuggestions(matches, showHistory = false) {
    const suggestions = document.getElementById('suggestions');
    suggestions.innerHTML = '';

    // Əgər axtarış tarixçəsi göstərilməlidirsə
    if (showHistory && matches.length === 0) {
        const history = searchHistory.getHistory();
        if (history.length > 0) {
            const historyHeader = document.createElement('div');
            historyHeader.className = 'suggestion-header';
            historyHeader.textContent = 'Son axtarışlar:';
            suggestions.appendChild(historyHeader);

            history.slice(0, 5).forEach(item => {
                const div = document.createElement('div');
                div.className = 'suggestion-item history-item';
                div.innerHTML = `<span class="history-icon">🕒</span> ${item}`;
                div.addEventListener('click', () => {
                    document.getElementById('searchBox').value = item;
                    suggestions.style.display = 'none';
                    renderResults(item);
                });
                suggestions.appendChild(div);
            });
        }
    }

    if (matches.length === 0 && !showHistory) {
        suggestions.style.display = 'none';
        return;
    }

    if (matches.length > 0) {
        // Əgər tarixçə varsa, ayırıcı əlavə et
        if (suggestions.children.length > 0) {
            const separator = document.createElement('div');
            separator.className = 'suggestion-separator';
            suggestions.appendChild(separator);
        }

        matches.forEach((item, index) => {
            const div = document.createElement('div');
            div.className = 'suggestion-item';

            // Highlight matching text
            const query = document.getElementById('searchBox').value.trim();
            const highlightedText = highlightMatch(item.tesiredici, query);
            div.innerHTML = highlightedText;

            div.addEventListener('click', () => {
                document.getElementById('searchBox').value = item.tesiredici;
                suggestions.style.display = 'none';
                renderResults(item.tesiredici);
            });

            // Klaviatura naviqasiyası üçün
            div.dataset.index = index;
            suggestions.appendChild(div);
        });
    }

    suggestions.style.display = 'block';
}

// Uyğun mətnləri vurğula
function highlightMatch(text, query) {
    if (!query) return text;

    const normalizedQuery = normalize(query);
    const normalizedText = normalize(text);

    const index = normalizedText.indexOf(normalizedQuery);
    if (index === -1) return text;

    // Orijinal mətndə uyğun hissəni tap
    const before = text.substring(0, index);
    const match = text.substring(index, index + query.length);
    const after = text.substring(index + query.length);

    return `${before}<mark>${match}</mark>${after}`;
}

// Nəticələri göstər - təkmilləşdirilmiş
function renderResults(query) {
    const container = document.getElementById('resultContainer');
    container.innerHTML = '';
    container.classList.remove('no-results');

    // Axtarışı tarixçəyə əlavə et
    if (query && query.trim()) {
        searchHistory.addSearch(query.trim());
    }

    const filtered = filterByTesiredici(query);

    if (filtered.length === 0) {
        const noResultsDiv = document.createElement('div');
        noResultsDiv.className = 'no-results-message';
        noResultsDiv.innerHTML = `
            <h3>Uyğun dərman tapılmadı</h3>
            <p>Axtarış: "<strong>${query}</strong>"</p>
            <p>Tövsiyələr:</p>
            <ul>
                <li>Yazılışı yoxlayın</li>
                <li>Daha qısa açar sözlər istifadə edin</li>
                <li>Müxtəlif sinonimləri sınayın</li>
            </ul>
        `;
        container.appendChild(noResultsDiv);
        container.classList.add('no-results');
        return;
    }

    // Nəticə sayını göstər
    const resultCount = document.createElement('div');
    resultCount.className = 'result-count';
    resultCount.textContent = `${filtered.length} dərman tapıldı`;
    container.appendChild(resultCount);

    const table = document.createElement('table');
    table.id = "resultTable";

    const thead = document.createElement('thead');
    const headerRow = document.createElement('tr');

    const th1 = document.createElement('th');
    th1.textContent = "Seç";

    const th2 = document.createElement('th');
    th2.style.display = 'flex';
    th2.style.justifyContent = 'space-between';
    th2.style.alignItems = 'center';

    const titleSpan = document.createElement('span');
    titleSpan.textContent = "Dərman Adı (Tərkib)";

    // Kopyalama düymələri konteyner
    const buttonContainer = document.createElement('div');
    buttonContainer.className = 'copy-buttons';

    const selectAllBtn = document.createElement('button');
    selectAllBtn.className = 'select-all-btn';
    selectAllBtn.textContent = "Hamısını Seç";
    selectAllBtn.addEventListener('click', () => {
        const checkboxes = document.querySelectorAll('input[type="checkbox"].drug-check');
        const allChecked = Array.from(checkboxes).every(cb => cb.checked);
        checkboxes.forEach(cb => cb.checked = !allChecked);
        selectAllBtn.textContent = allChecked ? "Hamısını Seç" : "Hamısını Ləğv Et";
    });

    const copyAllBtn = document.createElement('button');
    copyAllBtn.className = 'copy-all-btn';
    copyAllBtn.textContent = "Seçilənləri Kopyala";

    copyAllBtn.addEventListener('click', async () => {
        try {
            const checkboxes = document.querySelectorAll('input[type="checkbox"].drug-check');
            const selectedItems = [];

            checkboxes.forEach(checkbox => {
                if (checkbox.checked) {
                    selectedItems.push(checkbox.dataset.terkib);
                }
            });

            if (selectedItems.length === 0) {
                showNotification("Zəhmət olmasa ən azı bir dərmanı seçin.", "warning");
                return;
            }

            let htmlTable = `<table border="1" cellspacing="0" cellpadding="6" style="border-collapse:collapse; font-family: Arial, sans-serif; font-size: 14px;">`;
            htmlTable += `<tbody>`;

            selectedItems.forEach(terkib => {
                htmlTable += `<tr><td>${terkib}</td></tr>`;
            });

            htmlTable += `</tbody></table>`;

            await navigator.clipboard.write([
                new ClipboardItem({
                    "text/html": new Blob([htmlTable], { type: "text/html" }),
                    "text/plain": new Blob([selectedItems.join('\n')], { type: "text/plain" })
                })
            ]);

            copyAllBtn.textContent = "Kopyalandı!";
            showNotification(`${selectedItems.length} dərman kopyalandı`, "success");
            setTimeout(() => (copyAllBtn.textContent = "Seçilənləri Kopyala"), 1500);
        } catch (err) {
            console.error('Kopyalama xətası:', err);
            showNotification("Kopyalama mümkün olmadı: " + err.message, "error");
        }
    });

    buttonContainer.appendChild(selectAllBtn);
    buttonContainer.appendChild(copyAllBtn);

    th2.appendChild(titleSpan);
    th2.appendChild(buttonContainer);
    headerRow.appendChild(th1);
    headerRow.appendChild(th2);
    thead.appendChild(headerRow);
    table.appendChild(thead);

    const tbody = document.createElement('tbody');
    filtered.forEach(item => {
        const tr = document.createElement('tr');

        const checkboxTd = document.createElement('td');
        const checkbox = document.createElement('input');
        checkbox.type = 'checkbox';
        checkbox.checked = true;
        checkbox.className = 'drug-check';
        checkbox.dataset.terkib = item.terkib;
        checkboxTd.appendChild(checkbox);

        const dataTd = document.createElement('td');
        // Görünəndə dərman adı + (maddə) yazılsın
        dataTd.textContent = `${item.terkib} (${item.tesiredici})`;

        // Amma checkbox üçün sadəcə dərman adı getsin
        checkbox.dataset.terkib = item.terkib;
        
        tr.appendChild(checkboxTd);
        tr.appendChild(dataTd);
        tbody.appendChild(tr);
    });

    table.appendChild(tbody);
    container.appendChild(table);
}

// Klaviatura naviqasiyası
let currentSuggestionIndex = -1;

function handleKeyboardNavigation(e) {
    const suggestions = document.getElementById('suggestions');
    const suggestionItems = suggestions.querySelectorAll('.suggestion-item');

    if (suggestionItems.length === 0) return;

    switch (e.key) {
        case 'ArrowDown':
            e.preventDefault();
            currentSuggestionIndex = Math.min(currentSuggestionIndex + 1, suggestionItems.length - 1);
            updateSuggestionHighlight(suggestionItems);
            break;
        case 'ArrowUp':
            e.preventDefault();
            currentSuggestionIndex = Math.max(currentSuggestionIndex - 1, -1);
            updateSuggestionHighlight(suggestionItems);
            break;
        case 'Enter':
            e.preventDefault();
            if (currentSuggestionIndex >= 0 && currentSuggestionIndex < suggestionItems.length) {
                suggestionItems[currentSuggestionIndex].click();
            } else {
                const val = e.target.value.trim();
                if (val !== '') {
                    suggestions.style.display = 'none';
                    renderResults(val);
                }
            }
            break;
        case 'Escape':
            suggestions.style.display = 'none';
            currentSuggestionIndex = -1;
            break;
    }
}

function updateSuggestionHighlight(items) {
    items.forEach((item, index) => {
        item.classList.toggle('highlighted', index === currentSuggestionIndex);
    });
}

// Debounced axtarış funksiyası
const debouncedSearch = debounce((query) => {
    if (query.length >= 2) {
        const matches = getUniqueTesirediciSuggestions(query);
        showSuggestions(matches);
    } else if (query.length === 0) {
        showSuggestions([], true); // Tarixçəni göstər
    } else {
        document.getElementById('suggestions').style.display = 'none';
    }
    currentSuggestionIndex = -1;
}, 300);

window.addEventListener('DOMContentLoaded', async () => {
    // Loading indicator əlavə et
    const loadingDiv = document.createElement('div');
    loadingDiv.id = 'loadingIndicator';
    loadingDiv.innerHTML = '<div class="spinner"></div><span>Məlumatlar yüklənir...</span>';
    loadingDiv.style.display = 'none';
    document.body.appendChild(loadingDiv);

    await loadData();

    const searchBox = document.getElementById('searchBox');
    const suggestions = document.getElementById('suggestions');

    // Focus olduqda tarixçəni göstər
    searchBox.addEventListener('focus', () => {
        if (searchBox.value.trim() === '') {
            showSuggestions([], true);
        }
    });

    searchBox.addEventListener('input', (e) => {
        const val = e.target.value.trim();
        if (val === '') {
            document.getElementById('resultContainer').innerHTML = '';
        }
        debouncedSearch(val);
    });

    searchBox.addEventListener('keydown', handleKeyboardNavigation);

    document.addEventListener('click', (e) => {
        if (!e.target.closest('.search-wrapper')) {
            suggestions.style.display = 'none';
            currentSuggestionIndex = -1;
        }
    });

    // Tarixçəni təmizləmə düyməsi əlavə et
    const clearHistoryBtn = document.createElement('button');
    clearHistoryBtn.id = 'clearHistoryBtn';
    clearHistoryBtn.textContent = 'Tarixçəni Təmizlə';
    clearHistoryBtn.style.display = 'none';
    clearHistoryBtn.addEventListener('click', () => {
        searchHistory.clearHistory();
        showNotification('Axtarış tarixçəsi təmizləndi', 'info');
        suggestions.style.display = 'none';
    });

    // Search wrapper-ə əlavə et
    const searchWrapper = document.querySelector('.search-wrapper');
    if (searchWrapper) {
        searchWrapper.appendChild(clearHistoryBtn);
    }
});

// Tag sistemi - təkmilləşdirilmiş
let selectedTags = [];

function updateTagsUI() {
    const tagContainer = document.getElementById('tagContainer');
    if (!tagContainer) return;

    tagContainer.innerHTML = '';

    selectedTags.forEach((tag, index) => {
        const span = document.createElement('span');
        span.className = 'tag';
        span.innerHTML = `
            ${tag}
            <span class="remove" data-tag="${tag}" data-index="${index}" title="Sil">&times;</span>
        `;
        tagContainer.appendChild(span);
    });

    // Silmə funksiyası
    document.querySelectorAll('.remove').forEach(btn => {
        btn.addEventListener('click', (e) => {
            e.stopPropagation();
            const tagToRemove = btn.dataset.tag;
            selectedTags = selectedTags.filter(t => t !== tagToRemove);
            updateTagsUI();

            if (selectedTags.length > 0) {
                renderResults(selectedTags.join(','));
            } else {
                document.getElementById('resultContainer').innerHTML = '';
            }
        });
    });

    // Tag container-ı göstər/gizlət
    tagContainer.style.display = selectedTags.length > 0 ? 'block' : 'none';
}

// Tag əlavə etmə funksiyası
function addTag(tag) {
    const normalizedTag = tag.trim();
    if (!normalizedTag || selectedTags.includes(normalizedTag)) return false;

    selectedTags.push(normalizedTag);
    updateTagsUI();
    renderResults(selectedTags.join(','));
    return true;
}

// Bütün tag-ları təmizlə
function clearAllTags() {
    selectedTags = [];
    updateTagsUI();
    document.getElementById('resultContainer').innerHTML = '';
}

// Export/Import funksiyaları
function exportResults() {
    const checkboxes = document.querySelectorAll('input[type="checkbox"].drug-check:checked');
    const selectedDrugs = Array.from(checkboxes).map(cb => cb.dataset.terkib);

    if (selectedDrugs.length === 0) {
        showNotification('Eksport üçün dərman seçin', 'warning');
        return;
    }

    const data = {
        timestamp: new Date().toISOString(),
        query: selectedTags.join(','),
        drugs: selectedDrugs,
        count: selectedDrugs.length
    };

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `derman_siyahisi_${new Date().toISOString().split('T')[0]}.json`;
    a.click();
    URL.revokeObjectURL(url);

    showNotification(`${selectedDrugs.length} dərman eksport edildi`, 'success');
}

// Səhifə yükləndikdə işə salınacaq funksiyalar
document.addEventListener('DOMContentLoaded', function() {
    // Tema yüklə
    loadTheme();

    // Məlumatları yüklə
    loadData();

    // Event listener-lər
    const searchBox = document.getElementById('searchBox');
    const themeToggle = document.getElementById('themeToggle');
    const clearSearch = document.getElementById('clearSearch');
    const clearAllTags = document.getElementById('clearAllTags');
    const exportBtn = document.getElementById('exportBtn');
    const historyBtn = document.getElementById('historyBtn');

    // Axtarış qutusu event-ləri
    if (searchBox) {
        searchBox.addEventListener('input', debounce(function() {
            const query = this.value.trim();
            if (query.length > 0) {
                showSuggestions(query);
                // Clear düyməsini göstər
                if (clearSearch) {
                    clearSearch.style.opacity = '1';
                    clearSearch.style.visibility = 'visible';
                }
            } else {
                hideSuggestions();
                if (clearSearch) {
                    clearSearch.style.opacity = '0';
                    clearSearch.style.visibility = 'hidden';
                }
            }
        }, 300));

        // Klaviatura naviqasiyası
        searchBox.addEventListener('keydown', function(e) {
            const suggestions = document.getElementById('suggestions');
            const items = suggestions.querySelectorAll('.suggestion-item');

            if (e.key === 'ArrowDown') {
                e.preventDefault();
                navigateSuggestions(1, items);
            } else if (e.key === 'ArrowUp') {
                e.preventDefault();
                navigateSuggestions(-1, items);
            } else if (e.key === 'Enter') {
                e.preventDefault();
                const highlighted = suggestions.querySelector('.suggestion-item.highlighted');
                if (highlighted) {
                    highlighted.click();
                }
            } else if (e.key === 'Escape') {
                hideSuggestions();
            }
        });
    }

    // Tema dəyişdirmə
    if (themeToggle) {
        themeToggle.addEventListener('click', toggleTheme);
    }

    // Axtarışı təmizlə
    if (clearSearch) {
        clearSearch.addEventListener('click', function() {
            searchBox.value = '';
            searchBox.focus();
            hideSuggestions();
            this.style.opacity = '0';
            this.style.visibility = 'hidden';
        });
    }

    // Bütün tag-ları təmizlə
    if (clearAllTags) {
        clearAllTags.addEventListener('click', clearAllTags);
    }

    // Eksport düyməsi
    if (exportBtn) {
        exportBtn.addEventListener('click', exportResults);
    }

    // Tarixçə düyməsi
    if (historyBtn) {
        historyBtn.addEventListener('click', function() {
            showSearchHistory();
        });
    }

    // Klik edərkən suggestion-ları gizlət
    document.addEventListener('click', function(e) {
        const suggestions = document.getElementById('suggestions');
        const searchWrapper = document.querySelector('.search-wrapper');

        if (suggestions && searchWrapper && !searchWrapper.contains(e.target)) {
            hideSuggestions();
        }
    });
});

// Debounce funksiyası
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Suggestion naviqasiyası
function navigateSuggestions(direction, items) {
    const currentHighlighted = document.querySelector('.suggestion-item.highlighted');
    let currentIndex = -1;

    if (currentHighlighted) {
        currentIndex = Array.from(items).indexOf(currentHighlighted);
        currentHighlighted.classList.remove('highlighted');
    }

    const newIndex = currentIndex + direction;

    if (newIndex >= 0 && newIndex < items.length) {
        items[newIndex].classList.add('highlighted');
        items[newIndex].scrollIntoView({ block: 'nearest' });
    }
}

// Suggestion-ları gizlət
function hideSuggestions() {
    const suggestions = document.getElementById('suggestions');
    if (suggestions) {
        suggestions.classList.remove('show');
        suggestions.style.display = 'none';
    }
}

// Axtarış tarixçəsini göstər
function showSearchHistory() {
    const history = searchHistory.getHistory();
    if (history.length === 0) {
        showNotification('Axtarış tarixçəsi boşdur', 'info');
        return;
    }

    let historyHtml = '<div class="history-modal"><h3>Axtarış Tarixçəsi</h3><ul>';
    history.forEach(item => {
        historyHtml += `<li onclick="searchFromHistory('${item}')">${item}</li>`;
    });
    historyHtml += '</ul><button onclick="clearSearchHistory()">Tarixçəni Təmizlə</button></div>';

    // Modal yaradıb göstər
    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.innerHTML = historyHtml;
    document.body.appendChild(modal);

    // Modal-ı bağlamaq üçün
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            modal.remove();
        }
    });
}

// Tarixçədən axtarış
function searchFromHistory(term) {
    const searchBox = document.getElementById('searchBox');
    if (searchBox) {
        searchBox.value = term;
        showSuggestions(term);
    }
    // Modal-ı bağla
    const modal = document.querySelector('.modal-overlay');
    if (modal) {
        modal.remove();
    }
}

// Axtarış tarixçəsini təmizlə
function clearSearchHistory() {
    searchHistory.clearHistory();
    showNotification('Axtarış tarixçəsi təmizləndi', 'success');
    // Modal-ı bağla
    const modal = document.querySelector('.modal-overlay');
    if (modal) {
        modal.remove();
    }
}
