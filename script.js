let allData = [];

// JSON faylını yüklə
async function loadData() {
    try {
        const res = await fetch('data.json');
        allData = await res.json();
    } catch (e) {
        alert("Məlumat yüklənərkən xəta baş verdi: " + e);
    }
}

// Normalizasiya funksiyası
function normalize(str) {
    return str
        .toLowerCase()
        .replace(/c/g, 's')
        .replace(/x/g, 'ch')
        .replace(/ş/g, 's')
        .replace(/ə/g, 'e')
        .replace(/ı/g, 'i')
        .replace(/ü/g, 'u')
        .replace(/ö/g, 'o')
        .replace(/ğ/g, 'g')
        .replace(/s/g, 'c')
        .replace(/k/g, 'c')
        .replace(/ç/g, 'c')
        .replace(/w/g, 'v')
        .replace(/y/g, 'i');
}

// Təkliflər üçün unikal tesiredici
function getUniqueTesirediciSuggestions(query) {
    if (!query) return [];
    const normQuery = normalize(query.trim());
    const seen = new Set();
    const unique = [];

    allData.forEach(item => {
        if (!item.tesiredici) return;
        const normTesiredici = normalize(item.tesiredici);
        if (normTesiredici.includes(normQuery) && !seen.has(normTesiredici)) {
            seen.add(normTesiredici);
            unique.push(item);
        }
    });

    return unique;
}

// Nəticələr üçün tam uyğun dərman siyahısı
function filterByTesiredici(query) {
    if (!query) return [];

    // Normalizasiya funksiyası
    const normalize = str =>
        str
            .toLowerCase()
            .replace(/ç/g, 'c')
            .replace(/ş/g, 's')
            .replace(/ə/g, 'e')
            .replace(/ı/g, 'i')
            .replace(/ü/g, 'u')
            .replace(/ö/g, 'o')
            .replace(/ğ/g, 'g')
            .replace(/w/g, 'v')
            .replace(/\s+/g, ' ') // birdən çox boşluğu tək boşluğa çevir
            .trim();

    // Bir neçə maddəni vergüllə ayırıb normallaşdırırıq
    const queries = query
        .split(',')
        .map(q => normalize(q))
        .filter(q => q.length > 0);

    return allData.filter(item => {
        if (!item.tesiredici) return false;

        const normalizedTesiredici = normalize(item.tesiredici);

        // Əgər hər hansısa bir sorğu tam uyğun gəlirsə, daxil et
        return queries.some(q => normalizedTesiredici === q);
    });
}




// Tövsiyələri göstər
function showSuggestions(matches) {
    const suggestions = document.getElementById('suggestions');
    suggestions.innerHTML = '';

    if (matches.length === 0) {
        suggestions.style.display = 'none';
        return;
    }

    matches.slice(0, 10).forEach(item => {
        const div = document.createElement('div');
        div.className = 'suggestion-item';
        div.textContent = item.tesiredici;
        div.addEventListener('click', () => {
            document.getElementById('searchBox').value = item.tesiredici;
            suggestions.style.display = 'none';
            renderResults(item.tesiredici);
        });
        suggestions.appendChild(div);
    });

    suggestions.style.display = 'block';
}

// Nəticələri göstər
function renderResults(query) {
    const container = document.getElementById('resultContainer');
    container.innerHTML = '';
    container.classList.remove('no-results');

    const filtered = filterByTesiredici(query);

    if (filtered.length === 0) {
        container.textContent = "Uyğun dərman tapılmadı.";
        container.classList.add('no-results');
        return;
    }

    const table = document.createElement('table');
    table.id = "resultTable";

    const thead = document.createElement('thead');
    const headerRow = document.createElement('tr');

    const th1 = document.createElement('th');
    th1.textContent = "Seç";

    const th2 = document.createElement('th');
    th2.style.display = 'flex';
    th2.style.justifyContent = 'space-between';
    th2.style.alignItems = 'center';

    const titleSpan = document.createElement('span');
    titleSpan.textContent = "Dərman Adı (Tərkib)";

    const copyAllBtn = document.createElement('button');
    copyAllBtn.className = 'copy-all-btn';
    copyAllBtn.textContent = "Seçilənləri Kopyala";

    copyAllBtn.addEventListener('click', async () => {
        try {
            const checkboxes = document.querySelectorAll('input[type="checkbox"].drug-check');
            const selectedItems = [];

            checkboxes.forEach(checkbox => {
                if (checkbox.checked) {
                    selectedItems.push(checkbox.dataset.terkib);
                }
            });

            if (selectedItems.length === 0) {
                alert("Zəhmət olmasa ən azı bir dərmanı seçin.");
                return;
            }

            let htmlTable = `<table border="1" cellspacing="0" cellpadding="6" style="border-collapse:collapse; font-family: Arial, sans-serif; font-size: 14px;">`;
            htmlTable += `<tbody>`;

            selectedItems.forEach(terkib => {
                htmlTable += `<tr><td>${terkib}</td></tr>`;
            });

            htmlTable += `</tbody></table>`;

            await navigator.clipboard.write([
                new ClipboardItem({
                    "text/html": new Blob([htmlTable], { type: "text/html" }),
                    "text/plain": new Blob([selectedItems.join('\n')], { type: "text/plain" })
                })
            ]);

            copyAllBtn.textContent = "Kopyalandı!";
            setTimeout(() => (copyAllBtn.textContent = "Seçilənləri Kopyala"), 1500);
        } catch (err) {
            alert("Kopyalama mümkün olmadı: " + err);
        }
    });

    th2.appendChild(titleSpan);
    th2.appendChild(copyAllBtn);
    headerRow.appendChild(th1);
    headerRow.appendChild(th2);
    thead.appendChild(headerRow);
    table.appendChild(thead);

    const tbody = document.createElement('tbody');
    filtered.forEach(item => {
        const tr = document.createElement('tr');

        const checkboxTd = document.createElement('td');
        const checkbox = document.createElement('input');
        checkbox.type = 'checkbox';
        checkbox.checked = true;
        checkbox.className = 'drug-check';
        checkbox.dataset.terkib = item.terkib;
        checkboxTd.appendChild(checkbox);

        const dataTd = document.createElement('td');
        // Görünəndə dərman adı + (maddə) yazılsın
        dataTd.textContent = `${item.terkib} (${item.tesiredici})`;

        // Amma checkbox üçün sadəcə dərman adı getsin
        checkbox.dataset.terkib = item.terkib;
        
        tr.appendChild(checkboxTd);
        tr.appendChild(dataTd);
        tbody.appendChild(tr);
    });

    table.appendChild(tbody);
    container.appendChild(table);
}

window.addEventListener('DOMContentLoaded', async () => {
    await loadData();

    const searchBox = document.getElementById('searchBox');
    const suggestions = document.getElementById('suggestions');

    searchBox.addEventListener('input', () => {
        const val = searchBox.value.trim();
        if (val === '') {
            suggestions.style.display = 'none';
            document.getElementById('resultContainer').innerHTML = '';
        } else {
            const matches = getUniqueTesirediciSuggestions(val);
            showSuggestions(matches);
        }
    });

    searchBox.addEventListener('keydown', (e) => {
        if (e.key === 'Enter') {
            e.preventDefault();
            const val = searchBox.value.trim();
            if (val !== '') {
                suggestions.style.display = 'none';
                renderResults(val);
            }
        }
    });

    document.addEventListener('click', (e) => {
        if (!e.target.closest('.search-wrapper')) {
            suggestions.style.display = 'none';
        }
    });
});

let selectedTags = [];

function updateTagsUI() {
    const tagContainer = document.getElementById('tagContainer');
    tagContainer.innerHTML = '';

    selectedTags.forEach(tag => {
        const span = document.createElement('span');
        span.className = 'tag';
        span.innerHTML = `${tag} <span class="remove" data-tag="${tag}">&times;</span>`;
        tagContainer.appendChild(span);
    });

    // Silmə funksiyası
    document.querySelectorAll('.remove').forEach(btn => {
        btn.addEventListener('click', () => {
            const tagToRemove = btn.dataset.tag;
            selectedTags = selectedTags.filter(t => t !== tagToRemove);
            updateTagsUI();
            renderResults(selectedTags.join(','));
        });
    });
}

document.getElementById('searchBox').addEventListener('keydown', (e) => {
    if (e.key === 'Enter') {
        e.preventDefault();
        const input = e.target.value.trim();
        if (input && !selectedTags.includes(input)) {
            selectedTags.push(input);
            updateTagsUI();
            renderResults(selectedTags.join(','));
            e.target.value = '';
        }
        document.getElementById('suggestions').style.display = 'none';
    }
});
