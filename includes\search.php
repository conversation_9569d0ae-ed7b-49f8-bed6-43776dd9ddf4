<?php
/**
 * <PERSON><PERSON><PERSON> Axtarış Sistemi - Axtarış Funksiyaları
 * 
 * Bu fayl axtarış əməliyyatları ilə bağlı funksiyaları ehtiva edir:
 * - <PERSON><PERSON>rman axtarışı
 * - Nəticəl<PERSON>rin filtrlənməsi və sıralanması
 * - Axtarış statistikaları
 */

require_once __DIR__ . '/functions.php';

/**
 * Dərmanları axtarır və nəticələri qaytarır
 * 
 * @param string $query Axtarış sorğusu
 * @param array $drugsData Dərman məlumatları massivi
 * @param int $limit Maksimum nəticə sayı (default: 50)
 * @return array Axtarış nəticələri
 */
function searchDrugs($query, $drugsData, $limit = 50) {
    // Sorğunu validasiya et
    $validation = validateSearchQuery($query);
    if (!$validation['valid']) {
        return [
            'success' => false,
            'message' => $validation['message'],
            'results' => [],
            'total' => 0
        ];
    }
    
    $query = $validation['query'];
    $results = [];
    
    foreach ($drugsData as $index => $drug) {
        // Məlumatları yoxla
        if (!isset($drug['tesiredici']) || !isset($drug['terkib'])) {
            continue;
        }
        
        $tesiredici = trim($drug['tesiredici']);
        $terkib = trim($drug['terkib']);
        
        // Boş və ya keçərsiz təsiredici maddələri atla
        if (empty($tesiredici) || $tesiredici === '1' || $tesiredici === '0') {
            continue;
        }
        
        // Score hesabla
        $score = calculateSearchScore($query, $tesiredici, $terkib);
        
        if ($score > 0) {
            $results[] = [
                'drug' => $drug,
                'score' => $score,
                'index' => $index
            ];
        }
    }
    
    // Score-a görə sırala (yüksəkdən aşağıya)
    usort($results, function($a, $b) {
        if ($a['score'] === $b['score']) {
            // Eyni score-da alfabetik sırala
            return strcmp($a['drug']['terkib'], $b['drug']['terkib']);
        }
        return $b['score'] - $a['score'];
    });
    
    // Nəticələri məhdudlaşdır
    $totalResults = count($results);
    $results = array_slice($results, 0, $limit);
    
    return [
        'success' => true,
        'message' => '',
        'results' => $results,
        'total' => $totalResults,
        'showing' => count($results),
        'query' => $query
    ];
}

/**
 * Axtarış nəticələrini qruplaşdırır (təsiredici maddəyə görə)
 * 
 * @param array $searchResults Axtarış nəticələri
 * @return array Qruplaşdırılmış nəticələr
 */
function groupSearchResults($searchResults) {
    $grouped = [];
    
    foreach ($searchResults as $result) {
        $tesiredici = $result['drug']['tesiredici'];
        
        if (!isset($grouped[$tesiredici])) {
            $grouped[$tesiredici] = [
                'tesiredici' => $tesiredici,
                'drugs' => [],
                'count' => 0
            ];
        }
        
        $grouped[$tesiredici]['drugs'][] = $result;
        $grouped[$tesiredici]['count']++;
    }
    
    // Qrup sayına görə sırala
    uasort($grouped, function($a, $b) {
        return $b['count'] - $a['count'];
    });
    
    return $grouped;
}

/**
 * Axtarış statistikalarını hesablayır
 * 
 * @param array $searchResults Axtarış nəticələri
 * @return array Statistika məlumatları
 */
function getSearchStatistics($searchResults) {
    if (empty($searchResults)) {
        return [
            'total_drugs' => 0,
            'unique_ingredients' => 0,
            'top_ingredient' => null,
            'score_range' => ['min' => 0, 'max' => 0]
        ];
    }
    
    $ingredients = [];
    $scores = [];
    
    foreach ($searchResults as $result) {
        $tesiredici = $result['drug']['tesiredici'];
        $score = $result['score'];
        
        // Təsiredici maddələri say
        if (!isset($ingredients[$tesiredici])) {
            $ingredients[$tesiredici] = 0;
        }
        $ingredients[$tesiredici]++;
        
        // Score-ları topla
        $scores[] = $score;
    }
    
    // Ən çox rast gəlinən təsiredici maddə
    $topIngredient = null;
    if (!empty($ingredients)) {
        arsort($ingredients);
        $topIngredient = [
            'name' => array_key_first($ingredients),
            'count' => reset($ingredients)
        ];
    }
    
    return [
        'total_drugs' => count($searchResults),
        'unique_ingredients' => count($ingredients),
        'top_ingredient' => $topIngredient,
        'score_range' => [
            'min' => !empty($scores) ? min($scores) : 0,
            'max' => !empty($scores) ? max($scores) : 0
        ]
    ];
}

/**
 * Axtarış sorğusunu highlight edir
 * 
 * @param string $text Mətn
 * @param string $query Axtarış sorğusu
 * @return string Highlight edilmiş mətn
 */
function highlightSearchQuery($text, $query) {
    if (empty($query) || empty($text)) {
        return htmlspecialchars($text, ENT_QUOTES, 'UTF-8');
    }
    
    $normalizedQuery = normalize($query);
    $normalizedText = normalize($text);
    
    // Uyğunluq tapılırsa highlight et
    if (strpos($normalizedText, $normalizedQuery) !== false) {
        $pattern = '/(' . preg_quote($query, '/') . ')/iu';
        $highlighted = preg_replace($pattern, '<mark>$1</mark>', $text);
        return $highlighted;
    }
    
    return htmlspecialchars($text, ENT_QUOTES, 'UTF-8');
}
