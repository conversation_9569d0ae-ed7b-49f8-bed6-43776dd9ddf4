/* ChatGPT-like Minimalist Design */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #10a37f; /* ChatGPT green */
    --primary-light: #1dd1a1;
    --primary-dark: #0d8c6f;
    --text-color: #343541; /* ChatGPT dark text */
    --text-light: #6e6e80; /* ChatGPT light text */
    --bg-color: #f7f7f8; /* ChatGPT light background */
    --bg-light: #ffffff;
    --bg-dark: #ececf1; /* ChatGPT slightly darker background */
    --border-color: #e5e5e5;
    --border-radius: 8px;
    --box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
    --transition: all 0.2s ease;
}

body {
    font-family: 'Söhne', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--bg-color);
    color: var(--text-color);
    line-height: 1.5;
    margin: 0;
    padding: 0;
}

a {
    text-decoration: none;
    color: var(--primary-color);
    transition: var(--transition);
}

a:hover {
    text-decoration: underline;
}

/* Container */
.container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
}

/* Header */
.header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 0;
    margin-bottom: 20px;
    border-bottom: 1px solid var(--border-color);
}

header {
    text-align: center;
    margin-bottom: 25px;
    padding: 20px 0;
    border-bottom: 1px solid var(--border-color);
}

header h1 {
    font-size: 24px;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 8px;
}

header p {
    color: var(--text-light);
    font-size: 14px;
}

/* Search Form */
.search-form {
    width: 100%;
    margin: 30px 0;
}

.search-wrapper {
    position: relative;
    margin-bottom: 15px;
}

/* Search Suggestions */
.search-suggestions {
    position: absolute;
    top: calc(100% - 1px);
    left: 0;
    right: 0;
    background: #ffffff;
    border: 1px solid #dfe1e5;
    border-top: none;
    border-radius: 0 0 24px 24px;
    box-shadow: 0 4px 6px rgba(32,33,36,.28);
    max-height: 350px;
    overflow-y: auto;
    z-index: 1000;
    display: none;
    margin-top: -1px;
}

.search-suggestions.show {
    display: block;
}

.suggestion-item {
    padding: 12px 20px;
    cursor: pointer;
    border-bottom: 1px solid #f1f3f4;
    display: flex;
    align-items: center;
    transition: background-color 0.1s ease;
}

.suggestion-item:hover,
.suggestion-item.highlighted {
    background-color: #f8f9fa;
}

.suggestion-item:last-child {
    border-bottom: none;
}

.suggestion-icon {
    margin-right: 12px;
    color: #9aa0a6;
    font-size: 14px;
}

.suggestion-text {
    flex: 1;
    color: #202124;
    font-size: 16px;
}

.suggestion-text .highlight {
    font-weight: 600;
    color: #1a73e8;
}

.suggestion-count {
    color: #5f6368;
    font-size: 13px;
    margin-left: 8px;
}

.loading-suggestions {
    padding: 12px 20px;
    text-align: center;
    color: #5f6368;
    font-size: 14px;
}

.no-suggestions {
    padding: 12px 20px;
    text-align: center;
    color: #5f6368;
    font-size: 14px;
}

.search-input-container {
    display: flex;
    align-items: center;
    width: 100%;
    background-color: #ffffff;
    border: 1px solid #dfe1e5;
    border-radius: 24px;
    padding: 12px 20px;
    box-shadow: 0 2px 5px 1px rgba(64,60,67,.16);
    transition: all 0.2s ease;
    margin: 20px 0;
}

.search-input-container:hover {
    box-shadow: 0 2px 8px 1px rgba(64,60,67,.24);
    border-color: rgba(223,225,229,0);
}

.search-input-container:focus-within {
    border-color: #4285f4;
    box-shadow: 0 2px 8px 1px rgba(64,60,67,.24);
}

.search-wrapper input,
.search-input {
    flex: 1;
    border: none;
    outline: none;
    font-size: 16px;
    color: #202124;
    background: transparent;
    padding: 8px 0;
    width: 100%;
    font-family: arial,sans-serif;
}

.search-wrapper input::placeholder,
.search-input::placeholder {
    color: #9aa0a6;
    font-size: 16px;
}

/* Search Form */
.search-form {
    width: 100%;
}

.search-box-container {
    position: relative;
    display: flex;
    align-items: center;
}

.search-button {
    position: absolute;
    right: 12px;
    background: transparent;
    border: none;
    padding: 8px;
    color: #5f6368;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 20px;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.search-button:hover {
    background: #f8f9fa;
    color: #202124;
}

.search-button:disabled {
    color: #dadce0;
    cursor: not-allowed;
}

.search-btn {
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    font-size: 14px;
    font-weight: 500;
    padding: 10px 16px;
    margin-left: 10px;
    cursor: pointer;
    transition: var(--transition);
}

.search-btn:hover {
    background-color: var(--primary-dark);
}

.search-examples {
    margin-top: 10px;
    font-size: 13px;
    color: var(--text-light);
    line-height: 1.4;
}

.search-examples a {
    margin: 0 2px;
    color: var(--primary-color);
    text-decoration: none;
    padding: 2px 4px;
    border-radius: 3px;
    transition: var(--transition);
}

.search-examples a:hover {
    background-color: rgba(16, 163, 127, 0.1);
    text-decoration: none;
}

#examplesLabel {
    font-weight: 500;
    margin-bottom: 10px;
    display: block;
    color: var(--text-color);
}

#examplesContainer {
    display: block;
}

.examples-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 12px;
    margin-top: 15px;
}

.search-example-link {
    display: flex;
    align-items: center;
    padding: 15px 18px;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 2px solid transparent;
    border-radius: 12px;
    text-decoration: none;
    color: var(--text-color);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    font-size: 14px;
    font-weight: 500;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    position: relative;
    overflow: hidden;
}

.search-example-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), #20c997);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.search-example-link:hover {
    background: linear-gradient(135deg, #ffffff 0%, rgba(16, 163, 127, 0.05) 100%);
    border-color: var(--primary-color);
    text-decoration: none;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(16, 163, 127, 0.15);
}

.search-example-link:hover::before {
    transform: scaleX(1);
}

.search-example-link:active {
    transform: translateY(0);
    box-shadow: 0 4px 12px rgba(16, 163, 127, 0.2);
}

.example-number {
    background: linear-gradient(135deg, var(--primary-color), #20c997);
    color: white;
    font-weight: 700;
    margin-right: 12px;
    min-width: 28px;
    height: 28px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    box-shadow: 0 2px 6px rgba(16, 163, 127, 0.3);
}

.example-text {
    flex: 1;
    font-weight: 500;
    line-height: 1.4;
}

/* Responsive design */
@media (max-width: 768px) {
    .examples-list {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .search-example-link {
        padding: 12px 15px;
        font-size: 13px;
    }

    .example-number {
        min-width: 24px;
        height: 24px;
        font-size: 11px;
        margin-right: 10px;
    }
}

.no-history-message {
    text-align: center;
    padding: 40px 20px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px;
    border: 2px dashed #dee2e6;
    margin-top: 15px;
}

.no-history-message p {
    margin: 0;
    font-size: 15px;
    color: #6c757d;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.no-history-message p::before {
    content: "🔍";
    font-size: 18px;
}

/* Example Forms */
.example-form {
    margin: 0;
    padding: 0;
}

.search-example-link {
    background: none;
    border: none;
    width: 100%;
    text-align: left;
    cursor: pointer;
}

/* Search Results */
.search-results {
    margin-top: 20px;
}

.results-header {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid var(--border-color);
}

.results-header h2 {
    color: var(--primary-color);
    margin-bottom: 5px;
}

.results-count {
    color: var(--text-light);
    margin: 5px 0;
    font-size: 14px;
}

.back-to-home {
    display: inline-block;
    margin-top: 10px;
    padding: 8px 16px;
    background-color: var(--bg-light);
    color: var(--primary-color);
    text-decoration: none;
    border-radius: var(--border-radius);
    border: 1px solid var(--primary-color);
    transition: var(--transition);
    font-size: 14px;
}

.back-to-home:hover {
    background-color: var(--primary-color);
    color: white;
}

.results-list {
    display: grid;
    gap: 15px;
}

.result-item {
    background: var(--bg-light);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 20px;
    transition: var(--transition);
}

.result-item:hover {
    border-color: var(--primary-color);
    box-shadow: 0 4px 12px rgba(16, 163, 127, 0.1);
}

.drug-name {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 8px;
    line-height: 1.4;
}

.drug-ingredient {
    font-size: 14px;
    color: var(--text-light);
}

.drug-ingredient strong {
    color: var(--primary-color);
}

.no-results {
    text-align: center;
    padding: 40px 20px;
    background: var(--bg-light);
    border-radius: var(--border-radius);
    border: 2px dashed var(--border-color);
}

.no-results p {
    margin: 10px 0;
    color: var(--text-light);
}

.no-results p:first-child {
    font-size: 18px;
    font-weight: 500;
}

/* Error Message */
.error-message {
    background: #fee;
    border: 1px solid #fcc;
    border-radius: var(--border-radius);
    padding: 15px;
    margin: 20px 0;
    color: #c33;
}

.error-message p {
    margin: 0;
    font-weight: 500;
}

/* Search Statistics */
.search-stats {
    margin-top: 10px;
    padding: 10px;
    background: var(--bg-light);
    border-radius: var(--border-radius);
    border-left: 4px solid var(--primary-color);
}

.search-stats small {
    color: var(--text-light);
    font-size: 12px;
}

/* Debug Info */
.debug-info {
    margin-top: 8px;
    padding: 5px 8px;
    background: #f8f9fa;
    border-radius: 4px;
    border-left: 3px solid #007bff;
}

.debug-info small {
    color: #007bff;
    font-family: monospace;
    font-size: 11px;
}

.debug-footer {
    margin-top: 10px;
    padding: 8px;
    background: #f8f9fa;
    border-radius: var(--border-radius);
    text-align: center;
}

.debug-footer small {
    color: #6c757d;
    font-family: monospace;
}

/* Highlight */
mark {
    background: #fff3cd;
    color: #856404;
    padding: 1px 2px;
    border-radius: 2px;
    font-weight: 500;
}

/* Selected Tags Container */
.selected-tags-container {
    margin: 15px 0;
    padding: 15px;
    background-color: rgba(16, 163, 127, 0.05);
    border: 1px solid rgba(16, 163, 127, 0.2);
    border-radius: var(--border-radius);
}

.tags-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    font-weight: 500;
    color: var(--text-color);
}

.clear-all-btn {
    background-color: #dc3545;
    color: white;
    border: none;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    transition: var(--transition);
}

.clear-all-btn:hover {
    background-color: #c82333;
}

.selected-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.tag-item {
    display: inline-flex;
    align-items: center;
    background-color: var(--primary-color);
    color: white;
    padding: 6px 10px;
    border-radius: 20px;
    font-size: 13px;
    font-weight: 500;
    gap: 6px;
}

.tag-text {
    user-select: none;
}

.tag-remove {
    background: none;
    border: none;
    color: white;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    padding: 0;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
}

.tag-remove:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

/* Suggestions */
#suggestions {
    border: 1px solid var(--border-color);
    border-top: none;
    background-color: var(--bg-light);
    max-height: 300px;
    overflow-y: auto;
    position: absolute;
    width: 100%;
    box-sizing: border-box;
    z-index: 1000;
    border-radius: 0 0 var(--border-radius) var(--border-radius);
    box-shadow: var(--box-shadow);
    display: none;
}

.suggestion-item {
    padding: 12px 15px;
    cursor: pointer;
    font-size: 15px;
    border-bottom: 1px solid var(--border-color);
    transition: var(--transition);
    color: var(--text-color);
}

.suggestion-item:hover,
.suggestion-item.highlighted {
    background-color: rgba(16, 163, 127, 0.1);
    color: var(--primary-color);
}

.suggestion-item:last-child {
    border-bottom: none;
}

.suggestion-header {
    padding: 8px 15px;
    font-size: 12px;
    font-weight: 600;
    color: var(--text-light);
    background-color: var(--bg-dark);
    border-bottom: 1px solid var(--border-color);
}

.history-item {
    color: var(--text-light);
}

.history-icon {
    margin-right: 8px;
}

.suggestion-separator {
    height: 1px;
    background-color: var(--border-color);
    margin: 5px 0;
}

.suggestion-item mark {
    background-color: rgba(16, 163, 127, 0.2);
    color: var(--primary-color);
    padding: 1px 2px;
    border-radius: 2px;
}

/* Results Container */
#resultContainer {
    margin-top: 25px;
    font-size: 15px;
    color: var(--text-color);
    min-height: 100px;
}

.result-count {
    margin: 20px 0;
    padding: 10px 0;
    color: var(--text-light);
    font-size: 14px;
    border-bottom: 1px solid var(--border-color);
}

.search-info {
    margin: 15px 0;
    padding: 12px 15px;
    background-color: rgba(16, 163, 127, 0.05);
    border-left: 3px solid var(--primary-color);
    border-radius: var(--border-radius);
    font-size: 13px;
    color: var(--text-color);
    line-height: 1.4;
}

/* No Results */
.no-results {
    text-align: center;
    margin: 40px 0;
    padding: 30px;
    background-color: var(--bg-light);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

.no-results-message {
    text-align: center;
    margin: 40px 0;
    padding: 30px;
    background-color: var(--bg-light);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

.no-results-message h3 {
    font-size: 20px;
    margin-bottom: 15px;
    color: var(--text-color);
}

.no-results-message p {
    color: var(--text-light);
    margin-bottom: 10px;
}

.no-results-message ul {
    text-align: left;
    color: var(--text-light);
    margin: 15px 0;
    padding-left: 20px;
}

/* Initial Info and Search Tips */
.initial-info {
    text-align: center;
    padding: 25px;
    margin: 20px 0;
    background-color: var(--bg-light);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

.search-tips {
    margin-top: 20px;
    padding: 20px;
    background-color: rgba(16, 163, 127, 0.05);
    border-radius: var(--border-radius);
    text-align: left;
}

.search-tips h3 {
    color: var(--primary-color);
    margin-bottom: 15px;
    font-size: 16px;
}

.search-tips ul {
    margin: 15px 0;
    padding-left: 20px;
}

.search-tips li {
    margin-bottom: 8px;
    color: var(--text-color);
}

.search-tips code {
    background-color: var(--bg-dark);
    padding: 2px 6px;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
    color: var(--primary-color);
    font-weight: 500;
}

.search-tips em {
    color: var(--text-light);
    font-size: 13px;
}

/* Loading Indicator */
#loadingIndicator {
    display: none;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: var(--bg-light);
    padding: 20px 30px;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    z-index: 1000;
    text-align: center;
    color: var(--primary-color);
}

.spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid var(--border-color);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s ease-in-out infinite;
    margin-right: 10px;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Footer */
footer {
    margin-top: 40px;
    padding: 20px 0;
    border-top: 1px solid var(--border-color);
    color: var(--text-light);
    font-size: 14px;
    text-align: center;
}
/* Results Table */
#resultTable {
    width: 100%;
    border-collapse: collapse;
    background-color: var(--bg-light);
    font-size: 15px;
    box-shadow: var(--box-shadow);
    border-radius: var(--border-radius);
    overflow: hidden;
    border: 1px solid var(--border-color);
}

#resultTable th,
#resultTable td {
    border: 1px solid var(--border-color);
    padding: 12px 15px;
    vertical-align: middle;
}

#resultTable th {
    background-color: var(--primary-color);
    color: white;
    text-align: center;
    font-weight: 600;
    font-size: 14px;
}

#resultTable td {
    text-align: left;
    background-color: var(--bg-light);
}

#resultTable tr:nth-child(even) td {
    background-color: var(--bg-dark);
}

#resultTable tr:hover td {
    background-color: rgba(16, 163, 127, 0.05);
}

#resultTable input[type="checkbox"] {
    width: 18px;
    height: 18px;
    cursor: pointer;
    accent-color: var(--primary-color);
}

/* Copy Buttons */
.copy-buttons {
    display: flex;
    gap: 8px;
    align-items: center;
}

.copy-all-btn,
.select-all-btn {
    padding: 6px 12px;
    background-color: var(--primary-color);
    border: none;
    color: white;
    cursor: pointer;
    border-radius: var(--border-radius);
    font-size: 12px;
    font-weight: 500;
    transition: var(--transition);
}

.copy-all-btn:hover,
.select-all-btn:hover {
    background-color: var(--primary-dark);
}

.select-all-btn {
    background-color: transparent;
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
}

.select-all-btn:hover {
    background-color: rgba(16, 163, 127, 0.1);
}
/* Tags */
#tagContainer,
.tag-container {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 15px;
    padding: 10px 0;
}

.tag {
    background-color: rgba(16, 163, 127, 0.1);
    color: var(--primary-color);
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 14px;
    display: flex;
    align-items: center;
    border: 1px solid rgba(16, 163, 127, 0.2);
    transition: var(--transition);
}

.tag:hover {
    background-color: rgba(16, 163, 127, 0.15);
}

.tag .remove {
    margin-left: 8px;
    cursor: pointer;
    font-weight: bold;
    color: var(--primary-dark);
    padding: 2px 4px;
    border-radius: 50%;
    transition: var(--transition);
}

.tag .remove:hover {
    background-color: var(--primary-color);
    color: white;
}

/* Notifications */
#notificationContainer {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    max-width: 300px;
}

.notification {
    padding: 12px 16px;
    margin-bottom: 10px;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    font-size: 14px;
    animation: slideIn 0.3s ease;
}

.notification-info {
    background-color: #e3f2fd;
    color: #1976d2;
    border-left: 4px solid #1976d2;
}

.notification-success {
    background-color: #e8f5e9;
    color: #2e7d32;
    border-left: 4px solid #2e7d32;
}

.notification-warning {
    background-color: #fff3e0;
    color: #f57c00;
    border-left: 4px solid #f57c00;
}

.notification-error {
    background-color: #ffebee;
    color: #c62828;
    border-left: 4px solid #c62828;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Clear History Button */
#clearHistoryBtn {
    position: absolute;
    top: 100%;
    right: 0;
    margin-top: 5px;
    padding: 4px 8px;
    background-color: var(--text-light);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    font-size: 12px;
    cursor: pointer;
    transition: var(--transition);
}

#clearHistoryBtn:hover {
    background-color: var(--text-color);
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 15px;
        margin: 10px;
    }

    .search-input-container {
        border-radius: 20px;
        padding: 10px 16px;
    }

    .search-suggestions {
        border-radius: 0 0 20px 20px;
        max-height: 250px;
    }

    .suggestion-item {
        padding: 10px 16px;
    }

    .search-input {
        font-size: 16px; /* iOS zoom prevention */
    }

    .search-btn {
        margin-left: 0;
        margin-top: 10px;
        width: 100%;
    }

    .copy-buttons {
        flex-direction: column;
        gap: 4px;
    }

    .copy-all-btn,
    .select-all-btn {
        font-size: 11px;
        padding: 4px 8px;
    }

    #resultTable th,
    #resultTable td {
        padding: 8px 10px;
        font-size: 14px;
    }

    .tag {
        font-size: 12px;
        padding: 4px 8px;
    }

    #notificationContainer {
        top: 10px;
        right: 10px;
        left: 10px;
        max-width: none;
    }
}