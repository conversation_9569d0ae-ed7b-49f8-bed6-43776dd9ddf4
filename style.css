/* Body mərkəzl<PERSON><PERSON><PERSON><PERSON><PERSON>, rahat oxunaqlı font və yüngül fond */
body {
    background-color: #f7f9fc;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 0;
    padding: 0;
    color: #333;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
}

/* Container ağ fonda, kölgəli və yuvarlaq kənarlı */
.container {
    width: 800px;
    background: white;
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.1);
    padding: 30px 25px 40px;
    border-radius: 10px;
    box-sizing: border-box;
}

/* Başlıq mərkəzləşmiş və təmiz */
header {
    text-align: center;
    margin-bottom: 25px;
}

header h1 {
    font-weight: 700;
    font-size: 26px;
    margin-bottom: 8px;
    color: #2c3e50;
}

header p {
    font-size: 15px;
    color: #555;
}

/* <PERSON><PERSON><PERSON><PERSON><PERSON> qutusu */
.search-wrapper {
    position: relative;
    margin-bottom: 15px;
}

.search-wrapper input {
    width: 95%;
    padding: 12px 18px;
    font-size: 16px;
    border: 2px solid #ddd;
    border-radius: 8px;
    transition: border-color 0.3s ease;
}

.search-wrapper input:focus {
    outline: none;
    border-color: #4caf50;
    box-shadow: 0 0 8px #4caf50aa;
}

/* Tövsiyələr paneli */
#suggestions {
    border: 1px solid #ddd;
    border-top: none;
    background-color: #fff;
    max-height: 200px;
    overflow-y: auto;
    position: absolute;
    width: 100%;
    box-sizing: border-box;
    z-index: 1000;
    border-radius: 0 0 8px 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

#suggestions div {
    padding: 10px 15px;
    cursor: pointer;
    font-size: 15px;
    border-bottom: 1px solid #eee;
    transition: background-color 0.2s ease;
}

#suggestions div:hover {
    background-color: #e8f5e9;
}

/* Nəticə konteyneri */
#resultContainer {
    margin-top: 25px;
    font-size: 15px;
    color: #333;
    min-height: 100px;
}

/* Footer */
footer {
    margin-top: 30px;
    text-align: center;
    font-size: 13px;
    color: #aaa;
    user-select: none;
}
#resultTable {
    width: 100%;
    border-collapse: collapse;
    background-color: #ffffff;
    font-size: 15px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.07);
    border-radius: 8px;
    overflow: hidden;
}

#resultTable th,
#resultTable td {
    border: 1px solid #e0e0e0;
    padding: 10px 15px;
    vertical-align: middle;
}

#resultTable th {
    background-color: #4CAF50;
    color: white;
    text-align: center;
    font-weight: 600;
}

#resultTable td {
    text-align: left;
}

#resultTable input[type="checkbox"] {
    width: 18px;
    height: 18px;
    cursor: pointer;
}

.copy-all-btn {
    padding: 6px 14px;
    background-color: #4CAF50;
    border: none;
    color: white;
    cursor: pointer;
    border-radius: 6px;
    font-size: 14px;
    transition: background-color 0.2s ease;
}

.copy-all-btn:hover {
    background-color: #3e9143;
}
.tag-container {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    margin-bottom: 8px;
}

.tag {
    background-color: #e0f2f1;
    color: #00796b;
    padding: 4px 10px;
    border-radius: 20px;
    font-size: 14px;
    display: flex;
    align-items: center;
}

.tag .remove {
    margin-left: 8px;
    cursor: pointer;
    font-weight: bold;
}