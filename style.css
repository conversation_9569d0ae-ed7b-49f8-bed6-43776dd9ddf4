/* ===== CSS VARIABLES ===== */
:root {
  /* Light Theme Colors */
  --primary-color: #6366f1;
  --primary-hover: #5855eb;
  --primary-light: #e0e7ff;
  --secondary-color: #f59e0b;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --error-color: #ef4444;
  --info-color: #3b82f6;

  /* Background Colors */
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-tertiary: #f1f5f9;
  --bg-card: #ffffff;
  --bg-overlay: rgba(0, 0, 0, 0.5);

  /* Text Colors */
  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --text-muted: #94a3b8;
  --text-inverse: #ffffff;

  /* Border Colors */
  --border-color: #e2e8f0;
  --border-hover: #cbd5e1;
  --border-focus: var(--primary-color);

  /* Shadow */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;

  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;

  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;
}

/* Dark Theme */
[data-theme="dark"] {
  --bg-primary: #0f172a;
  --bg-secondary: #1e293b;
  --bg-tertiary: #334155;
  --bg-card: #1e293b;

  --text-primary: #f8fafc;
  --text-secondary: #cbd5e1;
  --text-muted: #94a3b8;

  --border-color: #334155;
  --border-hover: #475569;
}

/* ===== RESET & BASE STYLES ===== */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: var(--bg-primary);
  color: var(--text-primary);
  line-height: 1.6;
  transition: background-color var(--transition-normal), color var(--transition-normal);
  overflow-x: hidden;
}

/* ===== LOADING SCREEN ===== */
.loading-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  transition: opacity var(--transition-slow), visibility var(--transition-slow);
}

.loading-content {
  text-align: center;
  color: var(--text-inverse);
}

.loading-spinner {
  width: 60px;
  height: 60px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid var(--text-inverse);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto var(--spacing-lg);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-content h2 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: var(--spacing-sm);
}

.loading-content p {
  font-size: 1rem;
  opacity: 0.9;
}

/* ===== MAIN APP ===== */
.main-app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: var(--bg-primary);
}

/* ===== HEADER ===== */
.app-header {
  background: var(--bg-card);
  border-bottom: 1px solid var(--border-color);
  box-shadow: var(--shadow-sm);
  position: sticky;
  top: 0;
  z-index: 100;
  backdrop-filter: blur(10px);
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--spacing-md) var(--spacing-lg);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.logo-icon {
  font-size: 2rem;
  color: var(--primary-color);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

.logo-section h1 {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.theme-toggle,
.settings-btn {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: var(--radius-lg);
  background: var(--bg-secondary);
  color: var(--text-secondary);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--transition-fast);
  font-size: 1rem;
}

.theme-toggle:hover,
.settings-btn:hover {
  background: var(--primary-color);
  color: var(--text-inverse);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

/* ===== MAIN CONTENT ===== */
.main-content {
  flex: 1;
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--spacing-xl) var(--spacing-lg);
  width: 100%;
}

/* ===== SEARCH SECTION ===== */
.search-section {
  margin-bottom: var(--spacing-2xl);
}

.search-container {
  background: var(--bg-card);
  border-radius: var(--radius-xl);
  padding: var(--spacing-xl);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--border-color);
  transition: all var(--transition-normal);
}

.search-container:hover {
  box-shadow: var(--shadow-xl);
  transform: translateY(-2px);
}

.search-wrapper {
  position: relative;
  margin-bottom: var(--spacing-lg);
}

.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.search-icon {
  position: absolute;
  left: var(--spacing-md);
  color: var(--text-muted);
  font-size: 1.1rem;
  z-index: 2;
}

#searchBox {
  width: 100%;
  padding: var(--spacing-md) var(--spacing-xl) var(--spacing-md) 3rem;
  border: 2px solid var(--border-color);
  border-radius: var(--radius-lg);
  font-size: 1.1rem;
  font-weight: 500;
  background: var(--bg-primary);
  color: var(--text-primary);
  transition: all var(--transition-fast);
  outline: none;
}

#searchBox:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px var(--primary-light);
  transform: translateY(-1px);
}

#searchBox::placeholder {
  color: var(--text-muted);
  font-weight: 400;
}

.clear-search {
  position: absolute;
  right: var(--spacing-sm);
  width: 32px;
  height: 32px;
  border: none;
  border-radius: var(--radius-md);
  background: var(--bg-secondary);
  color: var(--text-muted);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--transition-fast);
  opacity: 0;
  visibility: hidden;
}

.clear-search:hover {
  background: var(--error-color);
  color: var(--text-inverse);
  transform: scale(1.1);
}

/* ===== SUGGESTIONS ===== */
.suggestions {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: var(--bg-card);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-xl);
  max-height: 400px;
  overflow-y: auto;
  z-index: 1000;
  margin-top: var(--spacing-xs);
  display: none;
}

.suggestions.show {
  display: block;
  animation: slideDown var(--transition-fast);
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.suggestion-item {
  padding: var(--spacing-md) var(--spacing-lg);
  cursor: pointer;
  transition: all var(--transition-fast);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.suggestion-item:last-child {
  border-bottom: none;
}

.suggestion-item:hover,
.suggestion-item.highlighted {
  background: var(--primary-light);
  color: var(--primary-color);
  transform: translateX(4px);
}

.suggestion-item mark {
  background: var(--secondary-color);
  color: var(--text-inverse);
  padding: 2px 4px;
  border-radius: var(--radius-sm);
  font-weight: 600;
}

.suggestion-header {
  padding: var(--spacing-sm) var(--spacing-lg);
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-muted);
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
}

.history-item {
  color: var(--text-secondary);
}

.history-icon {
  opacity: 0.7;
}

.suggestion-separator {
  height: 1px;
  background: var(--border-color);
  margin: var(--spacing-xs) 0;
}

/* ===== SEARCH STATS ===== */
.search-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.875rem;
  color: var(--text-muted);
  padding-top: var(--spacing-md);
  border-top: 1px solid var(--border-color);
}

#totalDrugs {
  font-weight: 600;
}

#searchTime {
  font-style: italic;
}

/* ===== TAGS SECTION ===== */
.tags-section {
  margin-bottom: var(--spacing-xl);
  background: var(--bg-card);
  border-radius: var(--radius-xl);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-color);
}

.tags-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
}

.tags-header h3 {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.clear-all-tags {
  padding: var(--spacing-sm) var(--spacing-md);
  background: var(--error-color);
  color: var(--text-inverse);
  border: none;
  border-radius: var(--radius-md);
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.clear-all-tags:hover {
  background: #dc2626;
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.tag-container {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
}

.tag {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
  color: var(--text-inverse);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-lg);
  font-size: 0.875rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  transition: all var(--transition-fast);
  box-shadow: var(--shadow-sm);
}

.tag:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.tag .remove {
  cursor: pointer;
  font-weight: 700;
  font-size: 1rem;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--transition-fast);
}

.tag .remove:hover {
  background: var(--error-color);
  transform: scale(1.1);
}

/* ===== WELCOME MESSAGE ===== */
.welcome-message {
  text-align: center;
  padding: var(--spacing-2xl);
  background: var(--bg-card);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--border-color);
}

.welcome-icon {
  font-size: 4rem;
  color: var(--primary-color);
  margin-bottom: var(--spacing-lg);
  opacity: 0.8;
}

.welcome-message h2 {
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

.welcome-message > p {
  font-size: 1.1rem;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xl);
}

.quick-tips {
  background: var(--bg-secondary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  margin-top: var(--spacing-xl);
  text-align: left;
}

.quick-tips h4 {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

.quick-tips ul {
  list-style: none;
  padding: 0;
}

.quick-tips li {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) 0;
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.quick-tips li i {
  color: var(--primary-color);
  width: 16px;
}

/* ===== FOOTER ===== */
.app-footer {
  background: var(--bg-card);
  border-top: 1px solid var(--border-color);
  padding: var(--spacing-lg) 0;
  margin-top: auto;
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.footer-info p {
  color: var(--text-muted);
  font-size: 0.875rem;
  margin: 0;
}

.footer-actions {
  display: flex;
  gap: var(--spacing-sm);
}

.footer-btn {
  width: 36px;
  height: 36px;
  border: none;
  border-radius: var(--radius-md);
  background: var(--bg-secondary);
  color: var(--text-secondary);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--transition-fast);
  font-size: 0.9rem;
}

.footer-btn:hover {
  background: var(--primary-color);
  color: var(--text-inverse);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

/* ===== NOTIFICATIONS ===== */
.notification-container {
  position: fixed;
  top: var(--spacing-lg);
  right: var(--spacing-lg);
  z-index: 10000;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.notification {
  background: var(--bg-card);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: var(--spacing-md) var(--spacing-lg);
  box-shadow: var(--shadow-xl);
  min-width: 300px;
  max-width: 400px;
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  animation: slideInRight var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.notification::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: var(--primary-color);
}

.notification.success::before {
  background: var(--success-color);
}

.notification.error::before {
  background: var(--error-color);
}

.notification.warning::before {
  background: var(--warning-color);
}

.notification.info::before {
  background: var(--info-color);
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.notification-icon {
  font-size: 1.2rem;
  color: var(--primary-color);
}

.notification.success .notification-icon {
  color: var(--success-color);
}

.notification.error .notification-icon {
  color: var(--error-color);
}

.notification.warning .notification-icon {
  color: var(--warning-color);
}

.notification.info .notification-icon {
  color: var(--info-color);
}

.notification-content {
  flex: 1;
}

.notification-title {
  font-weight: 600;
  font-size: 0.9rem;
  color: var(--text-primary);
  margin-bottom: 2px;
}

.notification-message {
  font-size: 0.85rem;
  color: var(--text-secondary);
  line-height: 1.4;
}

.notification-close {
  background: none;
  border: none;
  color: var(--text-muted);
  cursor: pointer;
  font-size: 1rem;
  padding: 2px;
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
}

.notification-close:hover {
  background: var(--bg-secondary);
  color: var(--text-primary);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  .header-content {
    padding: var(--spacing-sm) var(--spacing-md);
  }

  .logo-section h1 {
    font-size: 1.25rem;
  }

  .main-content {
    padding: var(--spacing-lg) var(--spacing-md);
  }

  .search-container {
    padding: var(--spacing-lg);
  }

  .footer-content {
    flex-direction: column;
    gap: var(--spacing-md);
    text-align: center;
  }

  .notification-container {
    left: var(--spacing-md);
    right: var(--spacing-md);
  }

  .notification {
    min-width: auto;
  }

  .welcome-message {
    padding: var(--spacing-xl);
  }

  .welcome-icon {
    font-size: 3rem;
  }

  .welcome-message h2 {
    font-size: 1.5rem;
  }
}

@media (max-width: 480px) {
  .header-content {
    padding: var(--spacing-sm);
  }

  .logo-section {
    gap: var(--spacing-sm);
  }

  .logo-icon {
    font-size: 1.5rem;
  }

  .logo-section h1 {
    font-size: 1.1rem;
  }

  .main-content {
    padding: var(--spacing-md) var(--spacing-sm);
  }

  .search-container {
    padding: var(--spacing-md);
  }

  #searchBox {
    font-size: 1rem;
    padding: var(--spacing-sm) var(--spacing-md) var(--spacing-sm) 2.5rem;
  }

  .search-icon {
    left: var(--spacing-sm);
    font-size: 1rem;
  }

  .welcome-message {
    padding: var(--spacing-lg);
  }

  .welcome-icon {
    font-size: 2.5rem;
  }

  .welcome-message h2 {
    font-size: 1.25rem;
  }

  .quick-tips {
    padding: var(--spacing-md);
  }
}