/* ChatGPT-like Minimalist Design */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #10a37f; /* ChatGPT green */
    --primary-light: #1dd1a1;
    --primary-dark: #0d8c6f;
    --text-color: #343541; /* ChatGPT dark text */
    --text-light: #6e6e80; /* ChatGPT light text */
    --bg-color: #f7f7f8; /* ChatGPT light background */
    --bg-light: #ffffff;
    --bg-dark: #ececf1; /* ChatGPT slightly darker background */
    --border-color: #e5e5e5;
    --border-radius: 8px;
    --box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
    --transition: all 0.2s ease;
}

body {
    font-family: 'Söhne', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--bg-color);
    color: var(--text-color);
    line-height: 1.5;
    margin: 0;
    padding: 0;
}

a {
    text-decoration: none;
    color: var(--primary-color);
    transition: var(--transition);
}

a:hover {
    text-decoration: underline;
}

/* Container */
.container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
}

/* Header */
.header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 0;
    margin-bottom: 20px;
    border-bottom: 1px solid var(--border-color);
}

header {
    text-align: center;
    margin-bottom: 25px;
    padding: 20px 0;
    border-bottom: 1px solid var(--border-color);
}

header h1 {
    font-size: 24px;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 8px;
}

header p {
    color: var(--text-light);
    font-size: 14px;
}

/* Search Form */
.search-form {
    width: 100%;
    margin: 30px 0;
}

.search-wrapper {
    position: relative;
    margin-bottom: 15px;
}

.search-input-container {
    display: flex;
    align-items: center;
    width: 100%;
    background-color: var(--bg-light);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 10px 15px;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
}

.search-input-container:focus-within {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(16, 163, 127, 0.2);
}

.search-wrapper input,
.search-input {
    flex: 1;
    border: none;
    outline: none;
    font-size: 16px;
    color: var(--text-color);
    background: transparent;
    padding: 5px 0;
    width: 100%;
}

.search-wrapper input::placeholder,
.search-input::placeholder {
    color: var(--text-light);
}

.search-btn {
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    font-size: 14px;
    font-weight: 500;
    padding: 10px 16px;
    margin-left: 10px;
    cursor: pointer;
    transition: var(--transition);
}

.search-btn:hover {
    background-color: var(--primary-dark);
}

.search-examples {
    margin-top: 10px;
    font-size: 13px;
    color: var(--text-light);
    line-height: 1.4;
}

.search-examples a {
    margin: 0 2px;
    color: var(--primary-color);
    text-decoration: none;
    padding: 2px 4px;
    border-radius: 3px;
    transition: var(--transition);
}

.search-examples a:hover {
    background-color: rgba(16, 163, 127, 0.1);
    text-decoration: none;
}

#examplesLabel {
    font-weight: 500;
    margin-right: 5px;
}

#examplesContainer {
    display: inline;
}

/* Suggestions */
#suggestions {
    border: 1px solid var(--border-color);
    border-top: none;
    background-color: var(--bg-light);
    max-height: 300px;
    overflow-y: auto;
    position: absolute;
    width: 100%;
    box-sizing: border-box;
    z-index: 1000;
    border-radius: 0 0 var(--border-radius) var(--border-radius);
    box-shadow: var(--box-shadow);
    display: none;
}

.suggestion-item {
    padding: 12px 15px;
    cursor: pointer;
    font-size: 15px;
    border-bottom: 1px solid var(--border-color);
    transition: var(--transition);
    color: var(--text-color);
}

.suggestion-item:hover,
.suggestion-item.highlighted {
    background-color: rgba(16, 163, 127, 0.1);
    color: var(--primary-color);
}

.suggestion-item:last-child {
    border-bottom: none;
}

.suggestion-header {
    padding: 8px 15px;
    font-size: 12px;
    font-weight: 600;
    color: var(--text-light);
    background-color: var(--bg-dark);
    border-bottom: 1px solid var(--border-color);
}

.history-item {
    color: var(--text-light);
}

.history-icon {
    margin-right: 8px;
}

.suggestion-separator {
    height: 1px;
    background-color: var(--border-color);
    margin: 5px 0;
}

.suggestion-item mark {
    background-color: rgba(16, 163, 127, 0.2);
    color: var(--primary-color);
    padding: 1px 2px;
    border-radius: 2px;
}

/* Results Container */
#resultContainer {
    margin-top: 25px;
    font-size: 15px;
    color: var(--text-color);
    min-height: 100px;
}

.result-count {
    margin: 20px 0;
    padding: 10px 0;
    color: var(--text-light);
    font-size: 14px;
    border-bottom: 1px solid var(--border-color);
}

/* No Results */
.no-results {
    text-align: center;
    margin: 40px 0;
    padding: 30px;
    background-color: var(--bg-light);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

.no-results-message {
    text-align: center;
    margin: 40px 0;
    padding: 30px;
    background-color: var(--bg-light);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

.no-results-message h3 {
    font-size: 20px;
    margin-bottom: 15px;
    color: var(--text-color);
}

.no-results-message p {
    color: var(--text-light);
    margin-bottom: 10px;
}

.no-results-message ul {
    text-align: left;
    color: var(--text-light);
    margin: 15px 0;
    padding-left: 20px;
}

/* Loading Indicator */
#loadingIndicator {
    display: none;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: var(--bg-light);
    padding: 20px 30px;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    z-index: 1000;
    text-align: center;
    color: var(--primary-color);
}

.spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid var(--border-color);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s ease-in-out infinite;
    margin-right: 10px;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Footer */
footer {
    margin-top: 40px;
    padding: 20px 0;
    border-top: 1px solid var(--border-color);
    color: var(--text-light);
    font-size: 14px;
    text-align: center;
}
/* Results Table */
#resultTable {
    width: 100%;
    border-collapse: collapse;
    background-color: var(--bg-light);
    font-size: 15px;
    box-shadow: var(--box-shadow);
    border-radius: var(--border-radius);
    overflow: hidden;
    border: 1px solid var(--border-color);
}

#resultTable th,
#resultTable td {
    border: 1px solid var(--border-color);
    padding: 12px 15px;
    vertical-align: middle;
}

#resultTable th {
    background-color: var(--primary-color);
    color: white;
    text-align: center;
    font-weight: 600;
    font-size: 14px;
}

#resultTable td {
    text-align: left;
    background-color: var(--bg-light);
}

#resultTable tr:nth-child(even) td {
    background-color: var(--bg-dark);
}

#resultTable tr:hover td {
    background-color: rgba(16, 163, 127, 0.05);
}

#resultTable input[type="checkbox"] {
    width: 18px;
    height: 18px;
    cursor: pointer;
    accent-color: var(--primary-color);
}

/* Copy Buttons */
.copy-buttons {
    display: flex;
    gap: 8px;
    align-items: center;
}

.copy-all-btn,
.select-all-btn {
    padding: 6px 12px;
    background-color: var(--primary-color);
    border: none;
    color: white;
    cursor: pointer;
    border-radius: var(--border-radius);
    font-size: 12px;
    font-weight: 500;
    transition: var(--transition);
}

.copy-all-btn:hover,
.select-all-btn:hover {
    background-color: var(--primary-dark);
}

.select-all-btn {
    background-color: transparent;
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
}

.select-all-btn:hover {
    background-color: rgba(16, 163, 127, 0.1);
}
/* Tags */
#tagContainer,
.tag-container {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 15px;
    padding: 10px 0;
}

.tag {
    background-color: rgba(16, 163, 127, 0.1);
    color: var(--primary-color);
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 14px;
    display: flex;
    align-items: center;
    border: 1px solid rgba(16, 163, 127, 0.2);
    transition: var(--transition);
}

.tag:hover {
    background-color: rgba(16, 163, 127, 0.15);
}

.tag .remove {
    margin-left: 8px;
    cursor: pointer;
    font-weight: bold;
    color: var(--primary-dark);
    padding: 2px 4px;
    border-radius: 50%;
    transition: var(--transition);
}

.tag .remove:hover {
    background-color: var(--primary-color);
    color: white;
}

/* Notifications */
#notificationContainer {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    max-width: 300px;
}

.notification {
    padding: 12px 16px;
    margin-bottom: 10px;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    font-size: 14px;
    animation: slideIn 0.3s ease;
}

.notification-info {
    background-color: #e3f2fd;
    color: #1976d2;
    border-left: 4px solid #1976d2;
}

.notification-success {
    background-color: #e8f5e9;
    color: #2e7d32;
    border-left: 4px solid #2e7d32;
}

.notification-warning {
    background-color: #fff3e0;
    color: #f57c00;
    border-left: 4px solid #f57c00;
}

.notification-error {
    background-color: #ffebee;
    color: #c62828;
    border-left: 4px solid #c62828;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Clear History Button */
#clearHistoryBtn {
    position: absolute;
    top: 100%;
    right: 0;
    margin-top: 5px;
    padding: 4px 8px;
    background-color: var(--text-light);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    font-size: 12px;
    cursor: pointer;
    transition: var(--transition);
}

#clearHistoryBtn:hover {
    background-color: var(--text-color);
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 15px;
        margin: 10px;
    }

    .search-input-container {
        flex-direction: column;
        padding: 10px;
    }

    .search-btn {
        margin-left: 0;
        margin-top: 10px;
        width: 100%;
    }

    .copy-buttons {
        flex-direction: column;
        gap: 4px;
    }

    .copy-all-btn,
    .select-all-btn {
        font-size: 11px;
        padding: 4px 8px;
    }

    #resultTable th,
    #resultTable td {
        padding: 8px 10px;
        font-size: 14px;
    }

    .tag {
        font-size: 12px;
        padding: 4px 8px;
    }

    #notificationContainer {
        top: 10px;
        right: 10px;
        left: 10px;
        max-width: none;
    }
}