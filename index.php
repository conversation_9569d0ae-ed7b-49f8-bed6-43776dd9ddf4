<?php
// Global axtarış tarixçəsini oxu
function getGlobalHistory() {
    $historyFile = 'global_search_history.json';
    
    if (!file_exists($historyFile)) {
        return [];
    }
    
    $content = file_get_contents($historyFile);
    $data = json_decode($content, true);
    
    return is_array($data) ? array_slice($data, 0, 10) : [];
}

$searchHistory = getGlobalHistory();
?>
<!DOCTYPE html>
<html lang="az">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Dərman Axtarış Sistemi - Təsiredici Maddə Əsasında</title>
  <meta name="description" content="Təsiredici maddə əsasında dərmanları asanlıqla tapın və seçin. Azərbaycan dərman bazası." />
  <meta name="keywords" content="dərman, təsir<PERSON>i maddə, ax<PERSON><PERSON><PERSON>, Azərbaycan, apteka" />
  <link rel="stylesheet" href="style.css" />
  <link rel="icon" type="image/x-icon" href="favicon.ico" />
</head>

<body>
  <div class="container">
    <!-- Header -->
    <header>
      <h1>Dərman Axtarış Sistemi</h1>
      <p>Təsiredici maddə əsasında dərmanları tapın</p>
    </header>

    <!-- Search Container -->
    <div class="search-container">
      <div class="search-wrapper">
        <div class="search-box-container">
          <input 
            type="text" 
            id="searchBox" 
            class="search-input"
            placeholder="Təsiredici maddə yazın və Enter basın (məsələn: PARACETAMOL)..." 
            autocomplete="off" 
            spellcheck="false"
          />
        </div>

        <!-- Suggestions -->
        <div id="suggestions"></div>
      </div>

      <!-- Selected Tags Container -->
      <div id="selectedTagsContainer" class="selected-tags-container" style="display: none;">
        <div class="tags-header">
          <span>Seçilmiş tərkiblər:</span>
          <button id="clearAllTags" class="clear-all-btn">Hamısını sil</button>
        </div>
        <div id="selectedTags" class="selected-tags"></div>
      </div>
    </div>

    <!-- Results Container -->
    <div id="resultContainer">
      <div class="initial-info">
        <h2 id="examplesLabel">Son axtarılanlar</h2>
        <div class="search-examples">
          <div id="examplesContainer">
            <?php if (!empty($searchHistory)): ?>
              <div class="examples-list">
                <?php foreach ($searchHistory as $index => $term): ?>
                  <a href="#" onclick="searchExample('<?= htmlspecialchars(addslashes($term), ENT_QUOTES, 'UTF-8') ?>')" class="search-example-link">
                    <span class="example-number"><?= $index + 1 ?></span>
                    <span class="example-text"><?= htmlspecialchars($term, ENT_QUOTES, 'UTF-8') ?></span>
                  </a>
                <?php endforeach; ?>
              </div>
            <?php else: ?>
              <div class="no-history-message">
                <p>Hələ heç bir axtarış edilməyib</p>
              </div>
            <?php endif; ?>
          </div>
        </div>
      </div>
    </div>

    <!-- Footer -->
    <footer>
      <p>© 2025 Dərman Axtarış Sistemi. Bütün hüquqlar qorunur.</p>
      <p>Bu sistem yalnız məlumat məqsədilə nəzərdə tutulub. Həkim məsləhəti almağı unutmayın.</p>
    </footer>
  </div>

  <!-- Loading Indicator -->
  <div id="loadingIndicator" style="display: none;">
    <div class="spinner"></div>
    <span>Məlumatlar yüklənir...</span>
  </div>

  <!-- Notification Container -->
  <div id="notificationContainer"></div>

  <script src="script.js"></script>
</body>

</html>
