<?php
// Axtarış funksiyaları
function loadDrugsData() {
    $dataFile = 'data.json';
    if (!file_exists($dataFile)) {
        return [];
    }

    $content = file_get_contents($dataFile);
    $data = json_decode($content, true);
    return is_array($data) ? $data : [];
}

// Normallaşdırma funksiyası
function normalize($text) {
    $text = mb_strtolower($text, 'UTF-8');
    $replacements = [
        'ə' => 'e', 'ı' => 'i', 'ö' => 'o', 'ü' => 'u', 'ç' => 'c', 'ş' => 's', 'ğ' => 'g',
        'Ə' => 'e', 'I' => 'i', 'Ö' => 'o', 'Ü' => 'u', 'Ç' => 'c', 'Ş' => 's', 'Ğ' => 'g'
    ];
    return strtr($text, $replacements);
}

// Global axtarış tarixçəsini oxu
function getGlobalHistory() {
    $historyFile = 'global_search_history.json';

    if (!file_exists($historyFile)) {
        return [];
    }

    $content = file_get_contents($historyFile);
    $data = json_decode($content, true);

    return is_array($data) ? array_slice($data, 0, 10) : [];
}

// Global tarixçəyə əlavə et
function addToGlobalHistory($searchTerm) {
    $historyFile = 'global_search_history.json';
    $maxHistoryItems = 10;

    $searchTerm = trim($searchTerm);
    if (empty($searchTerm) || strlen($searchTerm) < 2) {
        return false;
    }

    $history = getGlobalHistory();

    $normalizedSearch = normalize($searchTerm);

    // Mövcud olanı sil (case-insensitive)
    $history = array_filter($history, function($item) use ($normalizedSearch) {
        return normalize($item) !== $normalizedSearch;
    });

    // Başa əlavə et
    array_unshift($history, $searchTerm);

    // Maksimum sayda saxla
    if (count($history) > $maxHistoryItems) {
        $history = array_slice($history, 0, $maxHistoryItems);
    }

    // Faylı yenilə
    file_put_contents($historyFile, json_encode($history, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT));

    return true;
}

// Axtarış funksiyası
function searchDrugs($query, $drugsData) {
    if (empty($query) || strlen(trim($query)) < 2) {
        return [];
    }

    $query = trim($query);
    $normalizedQuery = normalize($query);
    $results = [];

    foreach ($drugsData as $drug) {
        $tesiredici = isset($drug['tesiredici']) ? $drug['tesiredici'] : '';
        $terkib = isset($drug['terkib']) ? $drug['terkib'] : '';

        if (empty($tesiredici) || $tesiredici === '1') continue;

        $normalizedTesiredici = normalize($tesiredici);
        $normalizedTerkib = normalize($terkib);

        $score = 0;

        // Exact match
        if ($normalizedTesiredici === $normalizedQuery) {
            $score = 1000;
        }
        // Starts with
        elseif (strpos($normalizedTesiredici, $normalizedQuery) === 0) {
            $score = 800;
        }
        // Contains in tesiredici
        elseif (strpos($normalizedTesiredici, $normalizedQuery) !== false) {
            $score = 600;
        }
        // Contains in terkib
        elseif (strpos($normalizedTerkib, $normalizedQuery) !== false) {
            $score = 400;
        }

        if ($score > 0) {
            $results[] = [
                'drug' => $drug,
                'score' => $score
            ];
        }
    }

    // Score-a görə sırala
    usort($results, function($a, $b) {
        return $b['score'] - $a['score'];
    });

    return array_slice($results, 0, 50); // İlk 50 nəticə
}

// POST sorğusu yoxla
$searchResults = [];
$searchQuery = '';
$showResults = false;

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['search_query'])) {
    $searchQuery = trim($_POST['search_query']);
    if (!empty($searchQuery)) {
        $drugsData = loadDrugsData();
        $searchResults = searchDrugs($searchQuery, $drugsData);
        $showResults = true;

        // Tarixçəyə əlavə et
        addToGlobalHistory($searchQuery);
    }
}

$searchHistory = getGlobalHistory();
?>
<!DOCTYPE html>
<html lang="az">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Dərman Axtarış Sistemi - Təsiredici Maddə Əsasında</title>
  <meta name="description" content="Təsiredici maddə əsasında dərmanları asanlıqla tapın və seçin. Azərbaycan dərman bazası." />
  <meta name="keywords" content="dərman, təsiredici maddə, axtarış, Azərbaycan, apteka" />
  <link rel="stylesheet" href="style.css" />
  <link rel="icon" type="image/x-icon" href="favicon.ico" />
</head>

<body>
  <div class="container">
    <!-- Header -->
    <header>
      <h1>Dərman Axtarış Sistemi</h1>
      <p>Təsiredici maddə əsasında dərmanları tapın</p>
    </header>

    <!-- Search Container -->
    <div class="search-container">
      <div class="search-wrapper">
        <form method="POST" action="" class="search-form">
          <div class="search-box-container">
            <input
              type="text"
              name="search_query"
              class="search-input"
              placeholder="Təsiredici maddə yazın (məsələn: PARACETAMOL)..."
              value="<?= htmlspecialchars($searchQuery, ENT_QUOTES, 'UTF-8') ?>"
              autocomplete="off"
              spellcheck="false"
              required
            />
            <button type="submit" class="search-button">
              <span>🔍</span>
            </button>
          </div>
        </form>
      </div>
    </div>

    <!-- Results Container -->
    <div id="resultContainer">
      <?php if ($showResults): ?>
        <!-- Search Results -->
        <div class="search-results">
          <div class="results-header">
            <h2>Axtarış nəticələri: "<?= htmlspecialchars($searchQuery, ENT_QUOTES, 'UTF-8') ?>"</h2>
            <p class="results-count"><?= count($searchResults) ?> nəticə tapıldı</p>
            <a href="?" class="back-to-home">← Ana səhifəyə qayıt</a>
          </div>

          <?php if (!empty($searchResults)): ?>
            <div class="results-list">
              <?php foreach ($searchResults as $result): ?>
                <?php $drug = $result['drug']; ?>
                <div class="result-item">
                  <div class="drug-name">
                    <?= htmlspecialchars($drug['terkib'], ENT_QUOTES, 'UTF-8') ?>
                  </div>
                  <div class="drug-ingredient">
                    <strong>Təsiredici:</strong> <?= htmlspecialchars($drug['tesiredici'], ENT_QUOTES, 'UTF-8') ?>
                  </div>
                </div>
              <?php endforeach; ?>
            </div>
          <?php else: ?>
            <div class="no-results">
              <p>🔍 Heç bir nəticə tapılmadı</p>
              <p>Başqa açar söz ilə cəhd edin</p>
            </div>
          <?php endif; ?>
        </div>
      <?php else: ?>
        <!-- Initial State - Search History -->
        <div class="initial-info">
          <h2>Son axtarılanlar</h2>
          <div class="search-examples">
            <div class="examples-container">
              <?php if (!empty($searchHistory)): ?>
                <div class="examples-list">
                  <?php foreach ($searchHistory as $index => $term): ?>
                    <form method="POST" action="" class="example-form">
                      <input type="hidden" name="search_query" value="<?= htmlspecialchars($term, ENT_QUOTES, 'UTF-8') ?>">
                      <button type="submit" class="search-example-link">
                        <span class="example-number"><?= $index + 1 ?></span>
                        <span class="example-text"><?= htmlspecialchars($term, ENT_QUOTES, 'UTF-8') ?></span>
                      </button>
                    </form>
                  <?php endforeach; ?>
                </div>
              <?php else: ?>
                <div class="no-history-message">
                  <p>Hələ heç bir axtarış edilməyib</p>
                </div>
              <?php endif; ?>
            </div>
          </div>
        </div>
      <?php endif; ?>
    </div>

    <!-- Footer -->
    <footer>
      <p>© 2025 Dərman Axtarış Sistemi. Bütün hüquqlar qorunur.</p>
      <p>Bu sistem yalnız məlumat məqsədilə nəzərdə tutulub. Həkim məsləhəti almağı unutmayın.</p>
    </footer>
  </div>

  <!-- Minimal JavaScript for form enhancement -->
  <script>
    // Form submit enhancement
    document.addEventListener('DOMContentLoaded', function() {
      const forms = document.querySelectorAll('form');
      forms.forEach(form => {
        form.addEventListener('submit', function() {
          const submitBtn = form.querySelector('button[type="submit"]');
          if (submitBtn) {
            submitBtn.disabled = true;
            submitBtn.innerHTML = submitBtn.innerHTML.includes('🔍') ? '⏳ Axtarılır...' : '⏳';
          }
        });
      });
    });
  </script>
</body>

</html>
