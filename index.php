<?php
/**
 * <PERSON><PERSON><PERSON>xtarış Sistemi - Ana <PERSON>ə
 *
 * Bu fayl sistemin ana səhifəsidir və istifadəçi interfeysi təmin edir
 */

// Xəta hesabatını aktiv et (development üçün)
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Lazımi faylları daxil et
require_once __DIR__ . '/includes/config.php';
require_once __DIR__ . '/includes/functions.php';
require_once __DIR__ . '/includes/search.php';
require_once __DIR__ . '/includes/history.php';

// Dəyişənləri təyin et
$searchResults = [];
$searchQuery = '';
$showResults = false;
$errorMessage = '';
$searchStats = null;

// POST sorğusunu işlə
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['search_query'])) {
    $searchQuery = sanitizeInput($_POST['search_query']);

    if (!empty($searchQuery)) {
        // Məlumatları yüklə
        $drugsData = loadDrugsData();

        if (empty($drugsData)) {
            $errorMessage = 'Dərman məlumatları yüklənə bilmədi. Zəhmət olmasa daha sonra cəhd edin.';
            logMessage("Dərman məlumatları yüklənə bilmədi", 'error');
        } else {
            // Axtarış et
            $searchResult = searchDrugs($searchQuery, $drugsData, MAX_SEARCH_RESULTS);

            if ($searchResult['success']) {
                $searchResults = $searchResult['results'];
                $showResults = true;
                $searchStats = getSearchStatistics($searchResults);

                // Tarixçəyə əlavə et
                addToGlobalHistory($searchQuery);

                // Axtarışı loq et
                if (LOG_SEARCHES) {
                    logMessage("Axtarış: '{$searchQuery}' - {$searchResult['total']} nəticə", 'search');
                }
            } else {
                $errorMessage = $searchResult['message'];
            }
        }
    } else {
        $errorMessage = 'Axtarış sorğusu boş ola bilməz.';
    }
}

// Tarixçəni yüklə
$searchHistory = getGlobalHistory(MAX_HISTORY_ITEMS);
?>
<!DOCTYPE html>
<html lang="az">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Dərman Axtarış Sistemi - Təsiredici Maddə Əsasında</title>
  <meta name="description" content="<?= htmlspecialchars(SYSTEM_DESCRIPTION, ENT_QUOTES, 'UTF-8') ?>" />
  <meta name="keywords" content="dərman, təsiredici maddə, axtarış, Azərbaycan, apteka" />
  <meta name="generator" content="<?= htmlspecialchars(SYSTEM_NAME . ' v' . SYSTEM_VERSION, ENT_QUOTES, 'UTF-8') ?>" />
  <link rel="stylesheet" href="style.css" />
  <link rel="icon" type="image/x-icon" href="favicon.ico" />
</head>

<body>
  <div class="container">
    <!-- Header -->
    <header>
      <h1><?= htmlspecialchars(SYSTEM_NAME, ENT_QUOTES, 'UTF-8') ?></h1>
      <p><?= htmlspecialchars(SYSTEM_DESCRIPTION, ENT_QUOTES, 'UTF-8') ?></p>
      <?php if (isDebugMode()): ?>
        <small style="color: #666;">v<?= SYSTEM_VERSION ?> - Debug Mode</small>
      <?php endif; ?>
    </header>

    <!-- Search Container -->
    <div class="search-container">
      <div class="search-wrapper">
        <form method="POST" action="" class="search-form">
          <div class="search-box-container">
            <input
              type="text"
              name="search_query"
              class="search-input"
              placeholder="Təsiredici maddə yazın (məsələn: PARACETAMOL)..."
              value="<?= htmlspecialchars($searchQuery, ENT_QUOTES, 'UTF-8') ?>"
              autocomplete="off"
              spellcheck="false"
              required
            />
            <button type="submit" class="search-button">
              <span>🔍</span>
            </button>
          </div>
        </form>
      </div>
    </div>

    <!-- Error Message -->
    <?php if (!empty($errorMessage)): ?>
      <div class="error-message">
        <p>⚠️ <?= htmlspecialchars($errorMessage, ENT_QUOTES, 'UTF-8') ?></p>
      </div>
    <?php endif; ?>

    <!-- Results Container -->
    <div id="resultContainer">
      <?php if ($showResults): ?>
        <!-- Search Results -->
        <div class="search-results">
          <div class="results-header">
            <h2>Axtarış nəticələri: "<?= htmlspecialchars($searchQuery, ENT_QUOTES, 'UTF-8') ?>"</h2>
            <p class="results-count">
              <?= count($searchResults) ?> nəticə tapıldı
              <?php if ($searchStats && $searchStats['total_drugs'] > count($searchResults)): ?>
                (<?= $searchStats['total_drugs'] ?> ümumi nəticədən <?= count($searchResults) ?> göstərilir)
              <?php endif; ?>
            </p>
            <a href="?" class="back-to-home">← Ana səhifəyə qayıt</a>

            <?php if (SHOW_SEARCH_STATISTICS && $searchStats): ?>
              <div class="search-stats">
                <small>
                  Unikal təsiredici maddələr: <?= $searchStats['unique_ingredients'] ?>
                  <?php if ($searchStats['top_ingredient']): ?>
                    | Ən çox: <?= htmlspecialchars($searchStats['top_ingredient']['name'], ENT_QUOTES, 'UTF-8') ?> (<?= $searchStats['top_ingredient']['count'] ?>)
                  <?php endif; ?>
                </small>
              </div>
            <?php endif; ?>
          </div>

          <?php if (!empty($searchResults)): ?>
            <div class="results-list">
              <?php foreach ($searchResults as $result): ?>
                <?php $drug = $result['drug']; ?>
                <div class="result-item">
                  <div class="drug-name">
                    <?php if (ENABLE_SEARCH_HIGHLIGHTING): ?>
                      <?= highlightSearchQuery($drug['terkib'], $searchQuery) ?>
                    <?php else: ?>
                      <?= htmlspecialchars($drug['terkib'], ENT_QUOTES, 'UTF-8') ?>
                    <?php endif; ?>
                  </div>
                  <div class="drug-ingredient">
                    <strong>Təsiredici:</strong>
                    <?php if (ENABLE_SEARCH_HIGHLIGHTING): ?>
                      <?= highlightSearchQuery($drug['tesiredici'], $searchQuery) ?>
                    <?php else: ?>
                      <?= htmlspecialchars($drug['tesiredici'], ENT_QUOTES, 'UTF-8') ?>
                    <?php endif; ?>
                  </div>
                  <?php if (isDebugMode()): ?>
                    <div class="debug-info">
                      <small>Score: <?= $result['score'] ?></small>
                    </div>
                  <?php endif; ?>
                </div>
              <?php endforeach; ?>
            </div>
          <?php else: ?>
            <div class="no-results">
              <p>🔍 Heç bir nəticə tapılmadı</p>
              <p>Başqa açar söz ilə cəhd edin</p>
            </div>
          <?php endif; ?>
        </div>
      <?php else: ?>
        <!-- Initial State - Search History -->
        <div class="initial-info">
          <h2>Son axtarılanlar</h2>
          <div class="search-examples">
            <div class="examples-container">
              <?php if (!empty($searchHistory)): ?>
                <div class="examples-list">
                  <?php foreach ($searchHistory as $index => $term): ?>
                    <form method="POST" action="" class="example-form">
                      <input type="hidden" name="search_query" value="<?= htmlspecialchars($term, ENT_QUOTES, 'UTF-8') ?>">
                      <button type="submit" class="search-example-link">
                        <span class="example-number"><?= $index + 1 ?></span>
                        <span class="example-text"><?= htmlspecialchars($term, ENT_QUOTES, 'UTF-8') ?></span>
                      </button>
                    </form>
                  <?php endforeach; ?>
                </div>
              <?php else: ?>
                <div class="no-history-message">
                  <p>Hələ heç bir axtarış edilməyib</p>
                </div>
              <?php endif; ?>
            </div>
          </div>
        </div>
      <?php endif; ?>
    </div>

    <!-- Footer -->
    <footer>
      <p>© 2025 <?= htmlspecialchars(SYSTEM_NAME, ENT_QUOTES, 'UTF-8') ?>. Bütün hüquqlar qorunur.</p>
      <p>Bu sistem yalnız məlumat məqsədilə nəzərdə tutulub. Həkim məsləhəti almağı unutmayın.</p>
      <?php if (isDebugMode()): ?>
        <div class="debug-footer">
          <small>
            PHP: <?= PHP_VERSION ?> |
            Memory: <?= round(memory_get_usage(true) / 1024 / 1024, 2) ?>MB |
            Time: <?= round((microtime(true) - $_SERVER['REQUEST_TIME_FLOAT']) * 1000, 2) ?>ms
          </small>
        </div>
      <?php endif; ?>
    </footer>
  </div>

  <!-- Minimal JavaScript for form enhancement -->
  <script>
    // Form submit enhancement
    document.addEventListener('DOMContentLoaded', function() {
      const forms = document.querySelectorAll('form');
      forms.forEach(form => {
        form.addEventListener('submit', function() {
          const submitBtn = form.querySelector('button[type="submit"]');
          if (submitBtn) {
            submitBtn.disabled = true;
            submitBtn.innerHTML = submitBtn.innerHTML.includes('🔍') ? '⏳ Axtarılır...' : '⏳';
          }
        });
      });
    });
  </script>
</body>

</html>
