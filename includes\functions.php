<?php
/**
 * <PERSON><PERSON>rman Axtarış Sistemi - <PERSON><PERSON><PERSON>
 * 
 * Bu fayl sistemin bütün əsas funksiyalarını ehtiva edir:
 * - Məlumat yükləmə və idarəetmə
 * - Axtarış alqoritmləri
 * - Tarixçə idarəetməsi
 * - Normallaşdırma funksiyaları
 */

/**
 * Dərman məlumatlarını JSON faylından yükləyir
 * 
 * @return array Dərman məlumatları massivi
 */
function loadDrugsData() {
    $dataFile = __DIR__ . '/../data.json';
    
    if (!file_exists($dataFile)) {
        error_log("Dərman məlumatları faylı tapılmadı: " . $dataFile);
        return [];
    }
    
    $content = file_get_contents($dataFile);
    if ($content === false) {
        error_log("Dərman məlumatları faylı oxuna bilmədi: " . $dataFile);
        return [];
    }
    
    $data = json_decode($content, true);
    if (json_last_error() !== JSON_ERROR_NONE) {
        error_log("JSON parse xətası: " . json_last_error_msg());
        return [];
    }
    
    return is_array($data) ? $data : [];
}

/**
 * Azərbaycan hərflərini normallaşdırır (case-insensitive axtarış üçün)
 * 
 * @param string $text Normallaşdırılacaq mətn
 * @return string Normallaşdırılmış mətn
 */
function normalize($text) {
    if (!is_string($text)) {
        return '';
    }
    
    $text = mb_strtolower(trim($text), 'UTF-8');
    
    $replacements = [
        'ə' => 'e', 'ı' => 'i', 'ö' => 'o', 'ü' => 'u', 
        'ç' => 'c', 'ş' => 's', 'ğ' => 'g',
        'Ə' => 'e', 'I' => 'i', 'Ö' => 'o', 'Ü' => 'u', 
        'Ç' => 'c', 'Ş' => 's', 'Ğ' => 'g'
    ];
    
    return strtr($text, $replacements);
}

/**
 * Mətn təmizləmə və validasiya
 * 
 * @param string $input Təmizlənəcək mətn
 * @return string Təmizlənmiş mətn
 */
function sanitizeInput($input) {
    if (!is_string($input)) {
        return '';
    }
    
    // HTML və PHP tag-larını təmizlə
    $input = strip_tags($input);
    
    // Əlavə boşluqları sil
    $input = trim($input);
    
    // Çox uzun mətnləri məhdudlaşdır
    if (mb_strlen($input, 'UTF-8') > 100) {
        $input = mb_substr($input, 0, 100, 'UTF-8');
    }
    
    return $input;
}

/**
 * Axtarış sorğusunu validasiya edir
 * 
 * @param string $query Axtarış sorğusu
 * @return array ['valid' => bool, 'message' => string, 'query' => string]
 */
function validateSearchQuery($query) {
    $query = sanitizeInput($query);
    
    if (empty($query)) {
        return [
            'valid' => false,
            'message' => 'Axtarış sorğusu boş ola bilməz',
            'query' => ''
        ];
    }
    
    if (mb_strlen($query, 'UTF-8') < 2) {
        return [
            'valid' => false,
            'message' => 'Axtarış sorğusu ən azı 2 hərf olmalıdır',
            'query' => $query
        ];
    }
    
    if (mb_strlen($query, 'UTF-8') > 50) {
        return [
            'valid' => false,
            'message' => 'Axtarış sorğusu çox uzundur (maksimum 50 hərf)',
            'query' => $query
        ];
    }
    
    return [
        'valid' => true,
        'message' => '',
        'query' => $query
    ];
}

/**
 * Axtarış nəticələrini score-a görə sıralayır
 * 
 * @param string $query Axtarış sorğusu
 * @param string $tesiredici Təsiredici maddə
 * @param string $terkib Dərman tərkibi
 * @return int Score (yüksək daha yaxşı)
 */
function calculateSearchScore($query, $tesiredici, $terkib) {
    $normalizedQuery = normalize($query);
    $normalizedTesiredici = normalize($tesiredici);
    $normalizedTerkib = normalize($terkib);
    
    $score = 0;
    
    // Exact match - ən yüksək prioritet
    if ($normalizedTesiredici === $normalizedQuery) {
        $score = 1000;
    }
    // Başlanğıc uyğunluğu
    elseif (strpos($normalizedTesiredici, $normalizedQuery) === 0) {
        $score = 800;
    }
    // Təsiredici maddədə ehtiva edir
    elseif (strpos($normalizedTesiredici, $normalizedQuery) !== false) {
        $score = 600;
    }
    // Dərman adında ehtiva edir
    elseif (strpos($normalizedTerkib, $normalizedQuery) !== false) {
        $score = 400;
    }
    
    // Uzunluq bonusu (qısa uyğunluqlar daha yaxşı)
    if ($score > 0) {
        $lengthDiff = abs(mb_strlen($normalizedTesiredici, 'UTF-8') - mb_strlen($normalizedQuery, 'UTF-8'));
        $score += max(0, 100 - $lengthDiff);
    }
    
    return $score;
}
